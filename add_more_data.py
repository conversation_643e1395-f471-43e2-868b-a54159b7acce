#!/usr/bin/env python3
"""
إضافة المزيد من البيانات التجريبية
"""

from database import *
from database import get_session
import datetime

def add_more_data():
    """إضافة المزيد من البيانات التجريبية"""
    session = get_session()
    
    try:
        print("🔧 إضافة المزيد من البيانات التجريبية...")
        
        # إضافة المزيد من الموردين
        existing_suppliers = session.query(Supplier).count()
        if existing_suppliers < 5:
            new_suppliers = [
                Supplier(name="شركة الأدوات الصحية المتطورة", phone="01234567900", email="<EMAIL>", address="الزمالك", balance=2500.0, notes="مورد أدوات صحية"),
                Supplier(name="مؤسسة البلاط والسيراميك", phone="01234567901", email="<EMAIL>", address="مصر الجديدة", balance=-1200.0, notes="مورد بلاط وسيراميك"),
                Supplier(name="شركة الزجاج والألومنيوم", phone="01234567902", email="<EMAIL>", address="الدقي", balance=1800.0, notes="مورد زجاج وألومنيوم"),
                Supplier(name="مصنع الأبواب والشبابيك", phone="01234567903", email="<EMAIL>", address="القطامية", balance=3200.0, notes="مورد أبواب وشبابيك"),
            ]
            for supplier in new_suppliers:
                session.add(supplier)
            session.commit()
            print(f"✅ تم إضافة {len(new_suppliers)} موردين جدد")
        
        # إضافة المزيد من العمال
        existing_employees = session.query(Employee).count()
        if existing_employees < 10:
            new_employees = [
                Employee(name="محمود أحمد علي", position="مهندس كهرباء", phone="01234567905", email="<EMAIL>", address="الجيزة", salary=5500.0, balance=800.0, notes="مهندس خبير"),
                Employee(name="نادية محمد حسن", position="مهندسة معمارية", phone="01234567906", email="<EMAIL>", address="المهندسين", salary=6500.0, balance=1200.0, notes="مهندسة تصميم"),
                Employee(name="عمرو سالم أحمد", position="عامل تشطيبات", phone="01234567907", email="<EMAIL>", address="الهرم", salary=3200.0, balance=400.0, notes="عامل ماهر"),
                Employee(name="ريم علي محمد", position="محاسبة", phone="01234567908", email="<EMAIL>", address="مدينة نصر", salary=4500.0, balance=600.0, notes="محاسبة خبيرة"),
                Employee(name="طارق حسن علي", position="مشرف موقع", phone="01234567909", email="<EMAIL>", address="النزهة", salary=5000.0, balance=700.0, notes="مشرف خبير"),
            ]
            for employee in new_employees:
                session.add(employee)
            session.commit()
            print(f"✅ تم إضافة {len(new_employees)} عمال جدد")
        
        # إضافة المزيد من المشاريع
        clients = session.query(Client).all()
        existing_projects = session.query(Project).count()
        if existing_projects < 10 and len(clients) >= 5:
            new_projects = [
                Project(name="مشروع مجمع سكني", description="بناء مجمع سكني من 5 عمارات", client_id=clients[5].id, budget=15000000.0, status="قيد التنفيذ", notes="مشروع ضخم"),
                Project(name="مشروع مدرسة خاصة", description="بناء مدرسة خاصة بجميع المرافق", client_id=clients[6].id, budget=8000000.0, status="معلق", notes="مشروع تعليمي"),
                Project(name="مشروع مستشفى صغير", description="بناء مستشفى صغير متخصص", client_id=clients[7].id, budget=12000000.0, status="قيد التنفيذ", notes="مشروع طبي"),
                Project(name="مشروع مول تجاري", description="بناء مول تجاري بـ 3 طوابق", client_id=clients[8].id, budget=20000000.0, status="مكتمل", notes="مشروع تجاري ناجح"),
                Project(name="مشروع فندق صغير", description="بناء فندق صغير 4 نجوم", client_id=clients[9].id, budget=18000000.0, status="قيد التنفيذ", notes="مشروع سياحي"),
            ]
            for project in new_projects:
                session.add(project)
            session.commit()
            print(f"✅ تم إضافة {len(new_projects)} مشاريع جديدة")
        
        # إضافة المزيد من عناصر المخزون
        suppliers = session.query(Supplier).all()
        existing_inventory = session.query(Inventory).count()
        if existing_inventory < 15 and suppliers:
            new_inventory = [
                Inventory(name="بلاط سيراميك", category="تشطيبات", unit="متر مربع", quantity=300, min_quantity=50, cost_price=45.0, selling_price=55.0, supplier_id=suppliers[0].id, location="مخزن التشطيبات", notes="بلاط عالي الجودة"),
                Inventory(name="أنابيب PVC", category="سباكة", unit="متر", quantity=200, min_quantity=30, cost_price=25.0, selling_price=35.0, supplier_id=suppliers[1].id, location="مخزن السباكة", notes="أنابيب مقاومة للضغط"),
                Inventory(name="مفاتيح كهرباء", category="كهربائيات", unit="قطعة", quantity=150, min_quantity=25, cost_price=15.0, selling_price=20.0, supplier_id=suppliers[2].id, location="مخزن الكهربائيات", notes="مفاتيح أوروبية"),
                Inventory(name="زجاج شفاف", category="زجاج", unit="متر مربع", quantity=80, min_quantity=15, cost_price=120.0, selling_price=150.0, supplier_id=suppliers[3].id, location="مخزن الزجاج", notes="زجاج مقاوم للكسر"),
                Inventory(name="أبواب خشبية", category="نجارة", unit="قطعة", quantity=25, min_quantity=5, cost_price=800.0, selling_price=1000.0, supplier_id=suppliers[4].id, location="مخزن النجارة", notes="أبواب خشب طبيعي"),
            ]
            for item in new_inventory:
                session.add(item)
            session.commit()
            print(f"✅ تم إضافة {len(new_inventory)} عناصر مخزون جديدة")
        
        # إضافة المزيد من العقارات
        projects = session.query(Project).all()
        existing_properties = session.query(Property).count()
        if existing_properties < 10 and len(projects) >= 5:
            new_properties = [
                Property(title="عمارة سكنية بالمنيل", description="عمارة 6 طوابق بالمنيل", project_id=projects[0].id, type="عمارة", area=800.0, price=12000000.0, status="available", location="المنيل، القاهرة"),
                Property(title="مصنع صغير بالعبور", description="مصنع صغير مجهز بالكامل", project_id=projects[1].id, type="مصنع", area=1200.0, price=5000000.0, status="sold", location="العبور، القليوبية"),
                Property(title="مخزن كبير بالسادس من أكتوبر", description="مخزن للتخزين والتوزيع", project_id=projects[2].id, type="مخزن", area=2000.0, price=3500000.0, status="available", location="السادس من أكتوبر، الجيزة"),
                Property(title="قطعة أرض بالعاصمة الإدارية", description="قطعة أرض للاستثمار", project_id=projects[3].id, type="أرض", area=1000.0, price=8000000.0, status="available", location="العاصمة الإدارية الجديدة"),
                Property(title="شاليه بالساحل الشمالي", description="شاليه مطل على البحر", project_id=projects[4].id, type="شاليه", area=200.0, price=4000000.0, status="sold", location="الساحل الشمالي، مطروح"),
            ]
            for property_item in new_properties:
                session.add(property_item)
            session.commit()
            print(f"✅ تم إضافة {len(new_properties)} عقارات جديدة")
        
        # إضافة المزيد من الإشعارات
        existing_reminders = session.query(Reminder).count()
        if existing_reminders < 10:
            new_reminders = [
                Reminder(title="فحص معدات الموقع", description="فحص دوري لمعدات الموقع", reminder_date=datetime.datetime.now() + datetime.timedelta(days=5), priority="medium", is_completed=False),
                Reminder(title="اجتماع مع المقاول", description="اجتماع لمناقشة تقدم العمل", reminder_date=datetime.datetime.now() + datetime.timedelta(days=8), priority="high", is_completed=False),
                Reminder(title="تسليم تقرير المشروع", description="تسليم التقرير الشهري للعميل", reminder_date=datetime.datetime.now() + datetime.timedelta(days=12), priority="high", is_completed=False),
                Reminder(title="صيانة المعدات", description="صيانة دورية للمعدات الثقيلة", reminder_date=datetime.datetime.now() + datetime.timedelta(days=20), priority="medium", is_completed=False),
                Reminder(title="تجديد التأمين", description="تجديد تأمين المشروع", reminder_date=datetime.datetime.now() + datetime.timedelta(days=25), priority="high", is_completed=True),
            ]
            for reminder in new_reminders:
                session.add(reminder)
            session.commit()
            print(f"✅ تم إضافة {len(new_reminders)} إشعارات جديدة")
        
        print("🎉 تم إضافة جميع البيانات الإضافية!")
        
        # طباعة إحصائيات محدثة
        print(f"\n📊 إحصائيات البيانات المحدثة:")
        print(f"👥 العملاء: {session.query(Client).count()}")
        print(f"🏭 الموردين: {session.query(Supplier).count()}")
        print(f"👷 العمال: {session.query(Employee).count()}")
        print(f"🏗️ المشاريع: {session.query(Project).count()}")
        print(f"💸 المصروفات: {session.query(Expense).count()}")
        print(f"💰 الإيرادات: {session.query(Revenue).count()}")
        print(f"📦 المخزون: {session.query(Inventory).count()}")
        print(f"🛒 المشتريات: {session.query(Purchase).count()}")
        print(f"🛍️ المبيعات: {session.query(Sale).count()}")
        print(f"🏠 العقارات: {session.query(Property).count()}")
        print(f"🔔 الإشعارات: {session.query(Reminder).count()}")
        print(f"📄 الفواتير: {session.query(Invoice).count()}")
        
    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في إضافة البيانات: {e}")
    finally:
        session.close()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إضافة المزيد من البيانات...")
    add_more_data()
    print("✅ تم الانتهاء من إضافة البيانات!")

if __name__ == "__main__":
    main()
