#!/usr/bin/env python3
"""
إصلاح جميع دوال الحذف لاستخدام الطريقة الآمنة
"""

import os
import re

# قائمة الأقسام والملفات المراد إصلاحها
sections = [
    {
        'file': 'ui/expenses.py',
        'model': 'Expense',
        'table': 'expenses_table',
        'item_name': 'مصروف',
        'function': 'delete_expense',
        'name_field': 'title'
    },
    {
        'file': 'ui/revenues.py',
        'model': 'Revenue',
        'table': 'revenues_table',
        'item_name': 'إيراد',
        'function': 'delete_revenue',
        'name_field': 'title'
    },
    {
        'file': 'ui/inventory.py',
        'model': 'Inventory',
        'table': 'inventory_table',
        'item_name': 'عنصر',
        'function': 'delete_item',
        'name_field': 'name'
    },
    {
        'file': 'ui/properties.py',
        'model': 'Property',
        'table': 'properties_table',
        'item_name': 'عقار',
        'function': 'delete_property',
        'name_field': 'title'
    },
    {
        'file': 'ui/reminders.py',
        'model': 'Reminder',
        'table': 'reminders_table',
        'item_name': 'تنبيه',
        'function': 'delete_reminder',
        'name_field': 'title'
    }
]

def fix_delete_function(section):
    """إصلاح دالة حذف واحدة"""
    file_path = section['file']
    
    if not os.path.exists(file_path):
        print(f"❌ الملف غير موجود: {file_path}")
        return False
    
    try:
        # قراءة الملف
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دالة الحذف
        function_pattern = rf'def {section["function"]}\(self\):(.*?)(?=\n    def|\nclass|\n\n\n|\Z)'
        match = re.search(function_pattern, content, re.DOTALL)
        
        if not match:
            print(f"❌ لم يتم العثور على دالة {section['function']} في {file_path}")
            return False
        
        # إنشاء الكود الجديد
        new_function = f'''def {section["function"]}(self):
        """حذف {section["item_name"]}"""
        from utils import safe_delete_item
        from database import {section["model"]}
        safe_delete_item(self, self.{section["table"]}, {section["model"]}, self.session, "{section["item_name"]}", "{section["name_field"]}")'''
        
        # استبدال الدالة
        new_content = re.sub(function_pattern, new_function, content, flags=re.DOTALL)
        
        # التأكد من وجود الاستيراد
        if 'from utils import' in new_content and 'safe_delete_item' not in new_content:
            # إضافة safe_delete_item إلى الاستيراد الموجود
            new_content = re.sub(
                r'from utils import ([^,\n]+)',
                r'from utils import \1, safe_delete_item',
                new_content
            )
        elif 'from utils import' not in new_content:
            # إضافة استيراد جديد
            new_content = re.sub(
                r'(from PyQt5\.QtGui import \*\n)',
                r'\1from utils import safe_delete_item\n',
                new_content
            )
        
        # كتابة الملف
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ تم إصلاح دالة الحذف في {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح {file_path}: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 بدء إصلاح جميع دوال الحذف...")
    
    success_count = 0
    total_count = len(sections)
    
    for section in sections:
        if fix_delete_function(section):
            success_count += 1
    
    print(f"\n📊 النتائج:")
    print(f"✅ تم إصلاح {success_count} من {total_count} دوال حذف")
    print(f"❌ فشل في إصلاح {total_count - success_count} دوال حذف")
    
    if success_count == total_count:
        print("🎉 تم إصلاح جميع دوال الحذف بنجاح!")
    else:
        print("⚠️ بعض دوال الحذف تحتاج إصلاح يدوي")

if __name__ == "__main__":
    main()
