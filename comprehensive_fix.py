#!/usr/bin/env python3
"""
إصلاح شامل لجميع مشاكل التعديل في البرنامج
"""

from database import *
from database import get_session
import datetime

def ensure_sample_data():
    """التأكد من وجود بيانات تجريبية كافية"""
    session = get_session()
    
    try:
        print("🔧 التحقق من البيانات التجريبية...")
        
        # إنشاء عملاء إذا لم يوجدوا
        if session.query(Client).count() == 0:
            clients = [
                Client(name="أحمد محمد علي", phone="01234567890", email="<EMAIL>", address="القاهرة، مصر", balance=1500.0, notes="عميل مميز"),
                Client(name="فاطمة حسن أحمد", phone="01234567891", email="<EMAIL>", address="الإسكندرية، مصر", balance=-500.0, notes="عميل جديد"),
                Client(name="محمد علي حسن", phone="01234567892", email="<EMAIL>", address="الجيزة، مصر", balance=2000.0, notes="عميل قديم"),
                Client(name="سارة أحمد محمد", phone="01234567893", email="<EMAIL>", address="الشرقية، مصر", balance=0.0, notes="عميل عادي"),
                Client(name="خالد حسن علي", phone="01234567894", email="<EMAIL>", address="المنوفية، مصر", balance=750.0, notes="عميل موثوق"),
            ]
            for client in clients:
                session.add(client)
            session.commit()
            print("✅ تم إنشاء 5 عملاء تجريبيين")
        
        # إنشاء موردين إذا لم يوجدوا
        if session.query(Supplier).count() == 0:
            suppliers = [
                Supplier(name="شركة المواد الإنشائية المتحدة", phone="01234567895", email="<EMAIL>", address="القاهرة الجديدة", balance=5000.0, notes="مورد رئيسي"),
                Supplier(name="مؤسسة الأدوات الكهربائية الحديثة", phone="01234567896", email="<EMAIL>", address="مدينة نصر", balance=-2000.0, notes="مورد كهربائيات"),
                Supplier(name="شركة الدهانات والتشطيبات", phone="01234567897", email="<EMAIL>", address="المعادي", balance=3000.0, notes="مورد دهانات"),
                Supplier(name="مصنع الأخشاب الطبيعية", phone="01234567898", email="<EMAIL>", address="العبور", balance=1500.0, notes="مورد أخشاب"),
                Supplier(name="شركة الحديد والصلب", phone="01234567899", email="<EMAIL>", address="حلوان", balance=-800.0, notes="مورد حديد"),
            ]
            for supplier in suppliers:
                session.add(supplier)
            session.commit()
            print("✅ تم إنشاء 5 موردين تجريبيين")
        
        # إنشاء عمال إذا لم يوجدوا
        if session.query(Employee).count() == 0:
            employees = [
                Employee(name="علي أحمد محمد", position="عامل بناء أول", phone="01234567800", email="<EMAIL>", address="شبرا الخيمة", salary=4000.0, balance=500.0, notes="عامل ماهر"),
                Employee(name="سارة محمد حسن", position="كهربائية معتمدة", phone="01234567801", email="<EMAIL>", address="المطرية", salary=4500.0, balance=-200.0, notes="فنية ماهرة"),
                Employee(name="خالد حسن علي", position="نجار خشب", phone="01234567802", email="<EMAIL>", address="عين شمس", salary=3500.0, balance=300.0, notes="نجار محترف"),
                Employee(name="منى علي أحمد", position="مهندسة موقع", phone="01234567803", email="<EMAIL>", address="مصر الجديدة", salary=6000.0, balance=1000.0, notes="مهندسة خبيرة"),
                Employee(name="أحمد محمد سالم", position="سباك", phone="01234567804", email="<EMAIL>", address="الزيتون", salary=3000.0, balance=0.0, notes="سباك ماهر"),
            ]
            for employee in employees:
                session.add(employee)
            session.commit()
            print("✅ تم إنشاء 5 عمال تجريبيين")
        
        # إنشاء مشاريع إذا لم توجد
        clients = session.query(Client).all()
        if session.query(Project).count() == 0 and clients:
            projects = [
                Project(name="مشروع فيلا العائلة الكريمة", description="بناء فيلا سكنية من 3 طوابق مع حديقة", client_id=clients[0].id, budget=800000.0, status="قيد التنفيذ", notes="مشروع مميز"),
                Project(name="مشروع مكتب الشركة التجارية", description="تشطيب مكتب تجاري بمساحة 200 متر", client_id=clients[1].id, budget=300000.0, status="مكتمل", notes="تم التسليم"),
                Project(name="مشروع شقة العروسين", description="تشطيب شقة سكنية 150 متر", client_id=clients[2].id, budget=250000.0, status="معلق", notes="في انتظار الموافقة"),
                Project(name="مشروع محل تجاري", description="تجهيز محل تجاري بالتكييف والإضاءة", client_id=clients[3].id, budget=150000.0, status="قيد التنفيذ", notes="مشروع سريع"),
                Project(name="مشروع مطعم شعبي", description="تجهيز مطعم بالمطبخ والصالة", client_id=clients[4].id, budget=400000.0, status="مكتمل", notes="مشروع ناجح"),
            ]
            for project in projects:
                session.add(project)
            session.commit()
            print("✅ تم إنشاء 5 مشاريع تجريبية")
        
        # إنشاء مصروفات إذا لم توجد
        suppliers = session.query(Supplier).all()
        if session.query(Expense).count() == 0 and suppliers:
            expenses = [
                Expense(title="شراء مواد بناء أساسية", amount=15000.0, supplier_id=suppliers[0].id, category="مواد بناء", status="مدفوع", notes="أسمنت وحديد ورمل"),
                Expense(title="أدوات كهربائية متنوعة", amount=8000.0, supplier_id=suppliers[1].id, category="أدوات كهربائية", status="معلق", notes="كابلات ومفاتيح وإضاءة"),
                Expense(title="دهانات وأدوات التشطيب", amount=5000.0, supplier_id=suppliers[2].id, category="تشطيبات", status="مدفوع", notes="دهان داخلي وخارجي"),
                Expense(title="أخشاب طبيعية للنجارة", amount=12000.0, supplier_id=suppliers[3].id, category="أخشاب", status="مدفوع", notes="خشب زان وموسكي"),
                Expense(title="حديد تسليح إضافي", amount=20000.0, supplier_id=suppliers[4].id, category="حديد", status="معلق", notes="حديد 16 و 12 ملم"),
            ]
            for expense in expenses:
                session.add(expense)
            session.commit()
            print("✅ تم إنشاء 5 مصروفات تجريبية")
        
        # إنشاء إيرادات إذا لم توجد
        if session.query(Revenue).count() == 0 and clients:
            revenues = [
                Revenue(title="دفعة مقدمة مشروع الفيلا", amount=200000.0, client_id=clients[0].id, category="دفعات مقدمة", status="مستلم", notes="40% من قيمة المشروع"),
                Revenue(title="دفعة نهائية المكتب التجاري", amount=100000.0, client_id=clients[1].id, category="دفعات نهائية", status="مستلم", notes="تم استلام المشروع"),
                Revenue(title="دفعة جزئية الشقة السكنية", amount=75000.0, client_id=clients[2].id, category="دفعات جزئية", status="معلق", notes="30% من القيمة"),
                Revenue(title="دفعة كاملة المحل التجاري", amount=150000.0, client_id=clients[3].id, category="دفعات كاملة", status="مستلم", notes="دفعة واحدة"),
                Revenue(title="دفعة مقدمة المطعم", amount=120000.0, client_id=clients[4].id, category="دفعات مقدمة", status="مستلم", notes="30% مقدم"),
            ]
            for revenue in revenues:
                session.add(revenue)
            session.commit()
            print("✅ تم إنشاء 5 إيرادات تجريبية")
        
        # إنشاء عناصر مخزون إذا لم توجد
        if session.query(Inventory).count() == 0 and suppliers:
            inventory_items = [
                Inventory(name="أسمنت بورتلاندي", category="مواد بناء", unit="شيكارة", quantity=200, min_quantity=50, cost_price=55.0, selling_price=65.0, supplier_id=suppliers[0].id, location="المخزن الرئيسي", notes="جودة عالية"),
                Inventory(name="حديد تسليح 16 ملم", category="حديد", unit="طن", quantity=10, min_quantity=2, cost_price=18000.0, selling_price=20000.0, supplier_id=suppliers[4].id, location="ساحة الحديد", notes="حديد عز"),
                Inventory(name="دهان بلاستيك أبيض", category="دهانات", unit="جالون", quantity=80, min_quantity=20, cost_price=85.0, selling_price=100.0, supplier_id=suppliers[2].id, location="مخزن الدهانات", notes="مقاوم للرطوبة"),
                Inventory(name="خشب زان طبيعي", category="أخشاب", unit="متر مكعب", quantity=15, min_quantity=3, cost_price=8000.0, selling_price=9500.0, supplier_id=suppliers[3].id, location="مخزن الأخشاب", notes="خشب مجفف"),
                Inventory(name="كابل كهرباء 2.5 ملم", category="كهربائيات", unit="متر", quantity=500, min_quantity=100, cost_price=12.0, selling_price=15.0, supplier_id=suppliers[1].id, location="مخزن الكهربائيات", notes="معتمد من الكهرباء"),
            ]
            for item in inventory_items:
                session.add(item)
            session.commit()
            print("✅ تم إنشاء 5 عناصر مخزون تجريبية")
        
        # إنشاء مشتريات إذا لم توجد
        if session.query(Purchase).count() == 0 and suppliers:
            purchases = [
                Purchase(purchase_number="P2024001", supplier_id=suppliers[0].id, total_amount=25000.0, status="مكتمل", notes="مواد بناء للمشروع الأول", purchase_date=datetime.date.today()),
                Purchase(purchase_number="P2024002", supplier_id=suppliers[1].id, total_amount=15000.0, status="معلق", notes="أدوات كهربائية متنوعة", purchase_date=datetime.date.today()),
                Purchase(purchase_number="P2024003", supplier_id=suppliers[2].id, total_amount=8000.0, status="مكتمل", notes="دهانات للتشطيبات", purchase_date=datetime.date.today()),
            ]
            for purchase in purchases:
                session.add(purchase)
            session.commit()
            print("✅ تم إنشاء 3 مشتريات تجريبية")
        
        # إنشاء مبيعات إذا لم توجد
        if session.query(Sale).count() == 0 and clients:
            sales = [
                Sale(client_id=clients[0].id, total_amount=50000.0, status="مكتمل", notes="بيع مواد فائضة", date=datetime.datetime.now()),
                Sale(client_id=clients[1].id, total_amount=25000.0, status="معلق", notes="بيع أدوات مستعملة", date=datetime.datetime.now()),
                Sale(client_id=clients[2].id, total_amount=15000.0, status="مكتمل", notes="بيع مواد تشطيب", date=datetime.datetime.now()),
            ]
            for sale in sales:
                session.add(sale)
            session.commit()
            print("✅ تم إنشاء 3 مبيعات تجريبية")
        
        # إنشاء عقارات إذا لم توجد
        if session.query(Property).count() == 0 and clients:
            properties = [
                Property(title="شقة سكنية بالمعادي", description="شقة 3 غرف وصالة بالمعادي", client_id=clients[0].id, property_type="شقة", area=150.0, price=2500000.0, status="متاح", location="المعادي، القاهرة", notes="شقة مميزة بإطلالة رائعة"),
                Property(title="فيلا بالتجمع الخامس", description="فيلا مستقلة بحديقة", client_id=clients[1].id, property_type="فيلا", area=400.0, price=8000000.0, status="مباع", location="التجمع الخامس، القاهرة الجديدة", notes="فيلا فاخرة مع مسبح"),
                Property(title="محل تجاري بوسط البلد", description="محل تجاري في موقع حيوي", client_id=clients[2].id, property_type="محل", area=80.0, price=1500000.0, status="مؤجر", location="وسط البلد، القاهرة", notes="محل في شارع رئيسي"),
                Property(title="مكتب إداري بمدينة نصر", description="مكتب إداري مجهز بالكامل", client_id=clients[3].id, property_type="مكتب", area=120.0, price=1800000.0, status="متاح", location="مدينة نصر، القاهرة", notes="مكتب حديث ومجهز"),
                Property(title="أرض زراعية بالفيوم", description="أرض زراعية خصبة", client_id=clients[4].id, property_type="أرض", area=5000.0, price=3000000.0, status="متاح", location="الفيوم", notes="أرض زراعية مروية"),
            ]
            for property_item in properties:
                session.add(property_item)
            session.commit()
            print("✅ تم إنشاء 5 عقارات تجريبية")

        # إنشاء إشعارات إذا لم توجد
        if session.query(Reminder).count() == 0:
            reminders = [
                Reminder(title="موعد اجتماع مع العميل", description="اجتماع مع العميل لمناقشة المشروع الجديد", reminder_date=datetime.date.today() + datetime.timedelta(days=1), priority="عالية", status="نشط", notes="اجتماع مهم"),
                Reminder(title="دفع فاتورة الكهرباء", description="دفع فاتورة الكهرباء للمكتب", reminder_date=datetime.date.today() + datetime.timedelta(days=3), priority="متوسطة", status="نشط", notes="فاتورة شهرية"),
                Reminder(title="مراجعة تقرير المشروع", description="مراجعة التقرير الشهري للمشروع", reminder_date=datetime.date.today() + datetime.timedelta(days=7), priority="منخفضة", status="نشط", notes="مراجعة دورية"),
                Reminder(title="تجديد رخصة البناء", description="تجديد رخصة البناء للمشروع الجديد", reminder_date=datetime.date.today() + datetime.timedelta(days=14), priority="عالية", status="نشط", notes="مهم جداً"),
                Reminder(title="اجتماع فريق العمل", description="اجتماع أسبوعي مع فريق العمل", reminder_date=datetime.date.today() + datetime.timedelta(days=2), priority="متوسطة", status="مكتمل", notes="اجتماع دوري"),
            ]
            for reminder in reminders:
                session.add(reminder)
            session.commit()
            print("✅ تم إنشاء 5 إشعارات تجريبية")

        # إنشاء فواتير إذا لم توجد
        if session.query(Invoice).count() == 0 and clients:
            invoices = [
                Invoice(client_id=clients[0].id, total_amount=50000.0, paid_amount=20000.0, status="partially_paid", due_date=datetime.date.today() + datetime.timedelta(days=30), notes="فاتورة مشروع الفيلا"),
                Invoice(client_id=clients[1].id, total_amount=30000.0, paid_amount=30000.0, status="paid", due_date=datetime.date.today() - datetime.timedelta(days=10), notes="فاتورة مكتملة الدفع"),
                Invoice(client_id=clients[2].id, total_amount=75000.0, paid_amount=0.0, status="pending", due_date=datetime.date.today() + datetime.timedelta(days=15), notes="فاتورة في انتظار الدفع"),
            ]
            for invoice in invoices:
                session.add(invoice)
            session.commit()
            print("✅ تم إنشاء 3 فواتير تجريبية")

        print("🎉 تم التأكد من وجود جميع البيانات التجريبية!")

        # طباعة إحصائيات
        print(f"\n📊 إحصائيات البيانات:")
        print(f"👥 العملاء: {session.query(Client).count()}")
        print(f"🏭 الموردين: {session.query(Supplier).count()}")
        print(f"👷 العمال: {session.query(Employee).count()}")
        print(f"🏗️ المشاريع: {session.query(Project).count()}")
        print(f"💸 المصروفات: {session.query(Expense).count()}")
        print(f"💰 الإيرادات: {session.query(Revenue).count()}")
        print(f"📦 المخزون: {session.query(Inventory).count()}")
        print(f"🛒 المشتريات: {session.query(Purchase).count()}")
        print(f"🛍️ المبيعات: {session.query(Sale).count()}")
        print(f"🏠 العقارات: {session.query(Property).count()}")
        print(f"🔔 الإشعارات: {session.query(Reminder).count()}")
        print(f"📄 الفواتير: {session.query(Invoice).count()}")
        
    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في إنشاء البيانات: {e}")
    finally:
        session.close()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء الإصلاح الشامل...")
    ensure_sample_data()
    print("✅ تم الانتهاء من الإصلاح الشامل!")

if __name__ == "__main__":
    main()
