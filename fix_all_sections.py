#!/usr/bin/env python3
"""
سكريبت لإصلاح جميع أقسام البرنامج لاستخدام الدالة الآمنة لاستخراج المعرفات
"""

import os
import re

# قائمة الأقسام والملفات المراد إصلاحها
sections = [
    {
        'file': 'ui/revenues.py',
        'model': 'Revenue',
        'dialog': 'RevenueDialog',
        'table': 'revenues_table',
        'item_name': 'إيراد',
        'function': 'edit_revenue'
    },
    {
        'file': 'ui/inventory.py',
        'model': 'Inventory',
        'dialog': 'InventoryItemDialog',
        'table': 'inventory_table',
        'item_name': 'عنصر',
        'function': 'edit_item'
    },
    {
        'file': 'ui/properties.py',
        'model': 'Property',
        'dialog': 'PropertyDialog',
        'table': 'properties_table',
        'item_name': 'عقار',
        'function': 'edit_property'
    },
    {
        'file': 'ui/projects.py',
        'model': 'Project',
        'dialog': 'ProjectDialog',
        'table': 'projects_table',
        'item_name': 'مشروع',
        'function': 'edit_project'
    },
    {
        'file': 'ui/reminders.py',
        'model': 'Reminder',
        'dialog': 'ReminderDialog',
        'table': 'reminders_table',
        'item_name': 'تنبيه',
        'function': 'edit_reminder'
    }
]

def fix_section(section):
    """إصلاح قسم واحد"""
    file_path = section['file']
    
    if not os.path.exists(file_path):
        print(f"❌ الملف غير موجود: {file_path}")
        return False
    
    try:
        # قراءة الملف
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دالة التعديل
        function_pattern = rf'def {section["function"]}\(self\):(.*?)(?=\n    def|\nclass|\n\n\n|\Z)'
        match = re.search(function_pattern, content, re.DOTALL)
        
        if not match:
            print(f"❌ لم يتم العثور على دالة {section['function']} في {file_path}")
            return False
        
        # إنشاء الكود الجديد
        new_function = f'''def {section["function"]}(self):
        """تعديل {section["item_name"]}"""
        from utils import safe_edit_item
        from database import {section["model"]}
        safe_edit_item(self, self.{section["table"]}, {section["model"]}, {section["dialog"]}, self.session, "{section["item_name"]}")'''
        
        # استبدال الدالة
        new_content = re.sub(function_pattern, new_function, content, flags=re.DOTALL)
        
        # التأكد من وجود الاستيراد
        if 'from utils import' in new_content and 'safe_edit_item' not in new_content:
            # إضافة safe_edit_item إلى الاستيراد الموجود
            new_content = re.sub(
                r'from utils import ([^,\n]+)',
                r'from utils import \1, safe_edit_item',
                new_content
            )
        elif 'from utils import' not in new_content:
            # إضافة استيراد جديد
            new_content = re.sub(
                r'(from PyQt5\.QtGui import \*\n)',
                r'\1from utils import safe_edit_item\n',
                new_content
            )
        
        # كتابة الملف
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ تم إصلاح {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح {file_path}: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 بدء إصلاح جميع الأقسام...")
    
    success_count = 0
    total_count = len(sections)
    
    for section in sections:
        if fix_section(section):
            success_count += 1
    
    print(f"\n📊 النتائج:")
    print(f"✅ تم إصلاح {success_count} من {total_count} أقسام")
    print(f"❌ فشل في إصلاح {total_count - success_count} أقسام")
    
    if success_count == total_count:
        print("🎉 تم إصلاح جميع الأقسام بنجاح!")
    else:
        print("⚠️ بعض الأقسام تحتاج إصلاح يدوي")

if __name__ == "__main__":
    main()
