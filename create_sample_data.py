#!/usr/bin/env python3
"""
سكريبت لإنشاء بيانات تجريبية في جميع أقسام البرنامج
"""

from database import *
from database import get_session
import datetime

def create_sample_clients(session):
    """إنشاء عملاء تجريبيين"""
    try:
        if session.query(Client).count() > 0:
            return
            
        clients = [
            Client(name="أحمد محمد", phone="01234567890", email="<EMAIL>", address="القاهرة", balance=1500.0),
            Client(name="فاطمة علي", phone="01234567891", email="<EMAIL>", address="الإسكندرية", balance=-500.0),
            Client(name="محمد حسن", phone="01234567892", email="<EMAIL>", address="الجيزة", balance=0.0),
        ]
        
        for client in clients:
            session.add(client)
        session.commit()
        print("✅ تم إنشاء العملاء التجريبيين")
        
    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في إنشاء العملاء: {e}")

def create_sample_suppliers(session):
    """إنشاء موردين تجريبيين"""
    try:
        if session.query(Supplier).count() > 0:
            return
            
        suppliers = [
            Supplier(name="شركة المواد الإنشائية", phone="01234567893", email="<EMAIL>", address="القاهرة", balance=2000.0),
            Supplier(name="مؤسسة الأدوات الكهربائية", phone="01234567894", email="<EMAIL>", address="الإسكندرية", balance=-800.0),
            Supplier(name="شركة الدهانات المتحدة", phone="01234567895", email="<EMAIL>", address="الجيزة", balance=1200.0),
        ]
        
        for supplier in suppliers:
            session.add(supplier)
        session.commit()
        print("✅ تم إنشاء الموردين التجريبيين")
        
    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في إنشاء الموردين: {e}")

def create_sample_employees(session):
    """إنشاء عمال تجريبيين"""
    try:
        if session.query(Employee).count() > 0:
            return
            
        employees = [
            Employee(name="علي أحمد", position="عامل بناء", phone="01234567896", email="<EMAIL>", address="القاهرة", salary=3000.0, balance=500.0),
            Employee(name="سارة محمد", position="كهربائية", phone="01234567897", email="<EMAIL>", address="الإسكندرية", salary=3500.0, balance=-200.0),
            Employee(name="خالد حسن", position="نجار", phone="01234567898", email="<EMAIL>", address="الجيزة", salary=2800.0, balance=0.0),
        ]
        
        for employee in employees:
            session.add(employee)
        session.commit()
        print("✅ تم إنشاء العمال التجريبيين")
        
    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في إنشاء العمال: {e}")

def create_sample_projects(session):
    """إنشاء مشاريع تجريبية"""
    try:
        if session.query(Project).count() > 0:
            return
            
        clients = session.query(Client).all()
        if not clients:
            return
            
        projects = [
            Project(name="مشروع فيلا سكنية", description="بناء فيلا سكنية من دورين", client_id=clients[0].id, budget=500000.0, status="قيد التنفيذ"),
            Project(name="مشروع مكتب تجاري", description="تشطيب مكتب تجاري", client_id=clients[1].id, budget=200000.0, status="مكتمل"),
            Project(name="مشروع شقة سكنية", description="تشطيب شقة سكنية", client_id=clients[2].id, budget=150000.0, status="معلق"),
        ]
        
        for project in projects:
            session.add(project)
        session.commit()
        print("✅ تم إنشاء المشاريع التجريبية")
        
    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في إنشاء المشاريع: {e}")

def create_sample_expenses(session):
    """إنشاء مصروفات تجريبية"""
    try:
        if session.query(Expense).count() > 0:
            return
            
        suppliers = session.query(Supplier).all()
        if not suppliers:
            return
            
        expenses = [
            Expense(title="شراء مواد بناء", amount=5000.0, supplier_id=suppliers[0].id, category="مواد", status="مدفوع"),
            Expense(title="أدوات كهربائية", amount=2000.0, supplier_id=suppliers[1].id, category="أدوات", status="معلق"),
            Expense(title="دهانات", amount=1500.0, supplier_id=suppliers[2].id, category="تشطيبات", status="مدفوع"),
        ]
        
        for expense in expenses:
            session.add(expense)
        session.commit()
        print("✅ تم إنشاء المصروفات التجريبية")
        
    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في إنشاء المصروفات: {e}")

def create_sample_revenues(session):
    """إنشاء إيرادات تجريبية"""
    try:
        if session.query(Revenue).count() > 0:
            return
            
        clients = session.query(Client).all()
        if not clients:
            return
            
        revenues = [
            Revenue(title="دفعة مقدمة فيلا", amount=100000.0, client_id=clients[0].id, category="دفعات", status="مستلم"),
            Revenue(title="دفعة نهائية مكتب", amount=50000.0, client_id=clients[1].id, category="دفعات", status="مستلم"),
            Revenue(title="دفعة جزئية شقة", amount=30000.0, client_id=clients[2].id, category="دفعات", status="معلق"),
        ]
        
        for revenue in revenues:
            session.add(revenue)
        session.commit()
        print("✅ تم إنشاء الإيرادات التجريبية")
        
    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في إنشاء الإيرادات: {e}")

def create_sample_inventory(session):
    """إنشاء عناصر مخزون تجريبية"""
    try:
        if session.query(Inventory).count() > 0:
            return
            
        suppliers = session.query(Supplier).all()
        supplier_id = suppliers[0].id if suppliers else None
            
        items = [
            Inventory(name="أسمنت", category="مواد بناء", unit="شيكارة", quantity=100, min_quantity=20, cost_price=50.0, selling_price=60.0, supplier_id=supplier_id),
            Inventory(name="حديد تسليح", category="مواد بناء", unit="طن", quantity=5, min_quantity=1, cost_price=15000.0, selling_price=18000.0, supplier_id=supplier_id),
            Inventory(name="دهان أبيض", category="دهانات", unit="جالون", quantity=50, min_quantity=10, cost_price=80.0, selling_price=100.0, supplier_id=supplier_id),
        ]
        
        for item in items:
            session.add(item)
        session.commit()
        print("✅ تم إنشاء عناصر المخزون التجريبية")
        
    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في إنشاء المخزون: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔧 بدء إنشاء البيانات التجريبية...")
    
    session = get_session()
    
    try:
        create_sample_clients(session)
        create_sample_suppliers(session)
        create_sample_employees(session)
        create_sample_projects(session)
        create_sample_expenses(session)
        create_sample_revenues(session)
        create_sample_inventory(session)
        
        print("\n🎉 تم إنشاء جميع البيانات التجريبية بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
    finally:
        session.close()

if __name__ == "__main__":
    main()
