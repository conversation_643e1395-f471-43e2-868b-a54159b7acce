#!/usr/bin/env python3
"""
إصلاح وإضافة بيانات تجريبية للمبيعات والمشتريات
"""

from database import *
from database import get_session
import datetime

def fix_sales_purchases():
    """إصلاح وإضافة بيانات للمبيعات والمشتريات"""
    session = get_session()
    
    try:
        print("🔧 إصلاح بيانات المبيعات والمشتريات...")
        
        # التأكد من وجود عملاء
        clients = session.query(Client).all()
        if not clients:
            print("❌ لا توجد عملاء في قاعدة البيانات")
            return
        
        # التأكد من وجود موردين
        suppliers = session.query(Supplier).all()
        if not suppliers:
            print("❌ لا توجد موردين في قاعدة البيانات")
            return
        
        # حذف المبيعات الموجودة وإعادة إنشائها
        session.query(Sale).delete()
        session.commit()
        
        # إنشاء مبيعات تجريبية جديدة
        new_sales = [
            Sale(
                client_id=clients[0].id,
                total_amount=15000.0,
                status='مكتمل',
                notes='بيع مواد بناء للعميل الأول',
                date=datetime.datetime.now() - datetime.timedelta(days=5)
            ),
            Sale(
                client_id=clients[1].id,
                total_amount=8500.0,
                status='معلق',
                notes='بيع أدوات كهربائية - في انتظار الدفع',
                date=datetime.datetime.now() - datetime.timedelta(days=3)
            ),
            Sale(
                client_id=clients[2].id,
                total_amount=12000.0,
                status='مكتمل',
                notes='بيع دهانات وأدوات تشطيب',
                date=datetime.datetime.now() - datetime.timedelta(days=1)
            ),
            Sale(
                client_id=clients[3].id,
                total_amount=25000.0,
                status='مكتمل',
                notes='بيع مواد إنشائية متنوعة',
                date=datetime.datetime.now()
            ),
            Sale(
                client_id=clients[4].id,
                total_amount=6500.0,
                status='ملغي',
                notes='تم إلغاء الطلب من قبل العميل',
                date=datetime.datetime.now() - datetime.timedelta(days=7)
            )
        ]
        
        for sale in new_sales:
            session.add(sale)
        session.commit()
        print(f"✅ تم إنشاء {len(new_sales)} مبيعات تجريبية")
        
        # حذف المشتريات الموجودة وإعادة إنشائها
        session.query(Purchase).delete()
        session.commit()
        
        # إنشاء مشتريات تجريبية جديدة
        new_purchases = [
            Purchase(
                purchase_number='P2024001',
                supplier_id=suppliers[0].id,
                total_amount=35000.0,
                status='completed',
                notes='شراء مواد بناء أساسية',
                date=datetime.datetime.now() - datetime.timedelta(days=10)
            ),
            Purchase(
                purchase_number='P2024002',
                supplier_id=suppliers[1].id,
                total_amount=18000.0,
                status='pending',
                notes='شراء أدوات كهربائية - في انتظار التسليم',
                date=datetime.datetime.now() - datetime.timedelta(days=5)
            ),
            Purchase(
                purchase_number='P2024003',
                supplier_id=suppliers[2].id,
                total_amount=12500.0,
                status='completed',
                notes='شراء دهانات وأدوات تشطيب',
                date=datetime.datetime.now() - datetime.timedelta(days=3)
            ),
            Purchase(
                purchase_number='P2024004',
                supplier_id=suppliers[3].id,
                total_amount=28000.0,
                status='completed',
                notes='شراء أخشاب طبيعية للنجارة',
                date=datetime.datetime.now() - datetime.timedelta(days=1)
            ),
            Purchase(
                purchase_number='P2024005',
                supplier_id=suppliers[4].id,
                total_amount=45000.0,
                status='pending',
                notes='شراء حديد تسليح - في انتظار الموافقة',
                date=datetime.datetime.now()
            )
        ]
        
        for purchase in new_purchases:
            session.add(purchase)
        session.commit()
        print(f"✅ تم إنشاء {len(new_purchases)} مشتريات تجريبية")
        
        print("🎉 تم إصلاح بيانات المبيعات والمشتريات بنجاح!")
        
        # طباعة إحصائيات
        print(f"\n📊 إحصائيات محدثة:")
        print(f"🛍️ المبيعات: {session.query(Sale).count()}")
        print(f"🛒 المشتريات: {session.query(Purchase).count()}")
        print(f"👥 العملاء: {session.query(Client).count()}")
        print(f"🏭 الموردين: {session.query(Supplier).count()}")
        
    except Exception as e:
        session.rollback()
        print(f"❌ خطأ في إصلاح البيانات: {e}")
    finally:
        session.close()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إصلاح المبيعات والمشتريات...")
    fix_sales_purchases()
    print("✅ تم الانتهاء من الإصلاح!")

if __name__ == "__main__":
    main()
