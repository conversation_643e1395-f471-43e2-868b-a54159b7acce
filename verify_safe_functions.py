#!/usr/bin/env python3
"""
التحقق من أن جميع دوال التعديل تستخدم الطريقة الآمنة
"""

import os
import re

def check_file_for_safe_edit(file_path):
    """التحقق من ملف واحد"""
    if not os.path.exists(file_path):
        return False, f"الملف غير موجود: {file_path}"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دوال التعديل
        edit_functions = re.findall(r'def (edit_\w+)\(self\):', content)
        
        results = []
        for func_name in edit_functions:
            # البحث عن محتوى الدالة
            func_pattern = rf'def {func_name}\(self\):(.*?)(?=\n    def|\nclass|\n\n\n|\Z)'
            match = re.search(func_pattern, content, re.DOTALL)
            
            if match:
                func_content = match.group(1)
                if 'safe_edit_item' in func_content:
                    results.append(f"✅ {func_name}: يستخدم safe_edit_item")
                elif 'int(' in func_content and '.text()' in func_content:
                    results.append(f"❌ {func_name}: يستخدم الطريقة القديمة الخطيرة")
                else:
                    results.append(f"⚠️ {func_name}: غير واضح")
            else:
                results.append(f"❓ {func_name}: لم يتم العثور على محتوى الدالة")
        
        return True, results
        
    except Exception as e:
        return False, f"خطأ في قراءة الملف: {e}"

def main():
    """الدالة الرئيسية"""
    print("🔍 التحقق من جميع دوال التعديل...")
    
    # قائمة الملفات المراد فحصها
    files_to_check = [
        'ui/clients.py',
        'ui/suppliers.py', 
        'ui/employees.py',
        'ui/projects.py',
        'ui/properties.py',
        'ui/expenses.py',
        'ui/revenues.py',
        'ui/inventory.py',
        'ui/purchases.py',
        'ui/sales.py',
        'ui/invoices.py',
        'ui/reminders.py'
    ]
    
    total_files = 0
    safe_files = 0
    
    for file_path in files_to_check:
        print(f"\n📁 فحص {file_path}:")
        success, results = check_file_for_safe_edit(file_path)
        
        if success:
            total_files += 1
            if isinstance(results, list):
                file_is_safe = True
                for result in results:
                    print(f"   {result}")
                    if "❌" in result:
                        file_is_safe = False
                
                if file_is_safe:
                    safe_files += 1
            else:
                print(f"   ℹ️ {results}")
        else:
            print(f"   ❌ {results}")
    
    print(f"\n📊 النتائج النهائية:")
    print(f"📁 إجمالي الملفات المفحوصة: {total_files}")
    print(f"✅ الملفات الآمنة: {safe_files}")
    print(f"❌ الملفات التي تحتاج إصلاح: {total_files - safe_files}")
    
    if safe_files == total_files:
        print("🎉 جميع الملفات تستخدم الطريقة الآمنة!")
    else:
        print("⚠️ بعض الملفات تحتاج إصلاح")

if __name__ == "__main__":
    main()
