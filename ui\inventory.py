from PyQt5.QtWidgets import (QWidget, Q<PERSON>oxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QGroupBox, QDateEdit, QDoubleSpinBox,
                            QSpinBox, QTabWidget, QSplitter, QFrame, QMenu, QAction, QSizePolicy,
                            QTextBrowser, QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QIcon, QFont, QColor, QPainter, QPixmap, QBrush, QPen, QLinearGradient

from database import (Inventory, Supplier, get_session)
from utils import (show_error_message, show_info_message, show_confirmation_message,
                    qdate_to_datetime, datetime_to_qdate, format_currency, format_quantity)
import datetime

from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, StyledLabel, StyledTabWidget)

class InventoryItemDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل عنصر في المخزون"""

    def __init__(self, parent=None, item=None, session=None):
        super().__init__(parent)
        self.item = item
        self.session = session
        self.parent_widget = parent  # حفظ مرجع للوالد
        self.init_ui()

    def customize_title_bar(self):
        """تخصيص شريط العنوان - مطابق تماماً للعملاء والموردين"""
        try:
            # إنشاء أيقونة مخصصة للمخزن مطابقة للعملاء والموردين
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # تدرج مطابق للعملاء والموردين
            from PyQt5.QtGui import QLinearGradient
            gradient = QLinearGradient(0, 0, 48, 48)
            gradient.setColorAt(0, QColor(59, 130, 246))  # أزرق
            gradient.setColorAt(0.5, QColor(147, 51, 234))  # بنفسجي
            gradient.setColorAt(1, QColor(236, 72, 153))  # وردي

            # رسم دائرة متدرجة
            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)

            # رسم رمز المخزن
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "📦")

            painter.end()

            # تعيين الأيقونة
            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تصميم متطور على شريط العنوان
            self.apply_advanced_title_bar_styling()

            # توسيط النص في شريط العنوان
            self.center_title_text()

        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم متطور على شريط العنوان مطابق للعملاء والموردين"""
        try:
            import platform
            if platform.system() == "Windows":
                # تطبيق ألوان شريط العنوان مطابقة للنافذة الرئيسية
                import ctypes
                from ctypes import wintypes

                # الحصول على handle النافذة
                hwnd = int(self.winId())

                # تعيين لون شريط العنوان مطابق للنافذة الرئيسية
                DWMWA_CAPTION_COLOR = 35
                color = 0x002A170F  # اللون الأساسي من شريط العنوان الرئيسي #0F172A بتنسيق BGR

                ctypes.windll.dwmapi.DwmSetWindowAttribute(
                    hwnd,
                    DWMWA_CAPTION_COLOR,
                    ctypes.byref(wintypes.DWORD(color)),
                    ctypes.sizeof(wintypes.DWORD)
                )

                # تعيين لون النص في شريط العنوان
                DWMWA_TEXT_COLOR = 36
                text_color = 0x00FFFFFF  # أبيض

                ctypes.windll.dwmapi.DwmSetWindowAttribute(
                    hwnd,
                    DWMWA_TEXT_COLOR,
                    ctypes.byref(wintypes.DWORD(text_color)),
                    ctypes.sizeof(wintypes.DWORD)
                )

        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")

    def center_title_text(self):
        """تحسين وضع النص في منتصف شريط العنوان"""
        try:
            # إضافة مسافات لتوسيط النص بصرياً
            original_title = "📦 تعديل عنصر المخزون - نظام إدارة المخزون المتطور والشامل" if self.item else "📦 إضافة عنصر جديد للمخزون - نظام إدارة المخزون المتطور والشامل"

            # حساب المسافات المطلوبة للتوسيط
            padding_spaces = "    "  # مسافات إضافية للتوسيط
            centered_title = f"{padding_spaces}{original_title}{padding_spaces}"

            # تحديث العنوان مع التوسيط
            self.setWindowTitle(centered_title)

        except Exception as e:
            print(f"تحذير: فشل في توسيط النص: {e}")

    def style_advanced_button(self, button, color_scheme='primary', has_menu=False):
        """تطبيق تصميم متطور للأزرار - مطابق للعملاء والموردين"""
        try:
            # ألوان مختلفة للأزرار
            color_schemes = {
                'primary': {
                    'base': '#2563EB',
                    'hover': '#1D4ED8',
                    'pressed': '#1E40AF',
                    'shadow': 'rgba(37, 99, 235, 0.4)'
                },
                'emerald': {
                    'base': '#10B981',
                    'hover': '#059669',
                    'pressed': '#047857',
                    'shadow': 'rgba(16, 185, 129, 0.4)'
                },
                'danger': {
                    'base': '#EF4444',
                    'hover': '#DC2626',
                    'pressed': '#B91C1C',
                    'shadow': 'rgba(239, 68, 68, 0.4)'
                }
            }

            colors = color_schemes.get(color_scheme, color_schemes['primary'])

            # مؤشر القائمة المنسدلة
            menu_indicator = ""
            if has_menu:
                menu_indicator = """
                    QPushButton::menu-indicator {
                        image: none;
                        width: 0px;
                    }
                """

            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {colors['base']}, stop:0.3 {colors['hover']},
                        stop:0.7 {colors['base']}, stop:1 {colors['pressed']});
                    color: white;
                    border: 3px solid {colors['pressed']};
                    border-radius: 12px;
                    padding: 12px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 4px 12px {colors['shadow']};
                    min-height: 35px;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {colors['hover']}, stop:0.3 {colors['pressed']},
                        stop:0.7 {colors['hover']}, stop:1 {colors['base']});
                    border: 3px solid {colors['base']};
                    transform: translateY(-2px) scale(1.03);
                    box-shadow: 0 6px 20px {colors['shadow']},
                               0 3px 12px rgba(0, 0, 0, 0.4);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {colors['pressed']}, stop:0.3 {colors['hover']},
                        stop:0.7 {colors['pressed']}, stop:1 {colors['base']});
                    transform: translateY(1px) scale(0.98);
                    box-shadow: 0 2px 8px {colors['shadow']};
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception as e:
            pass  # خطأ في تطبيق تصميم الزر

    def init_ui(self):
        # استخدام شريط العنوان الطبيعي للنظام مع النص في المنتصف - مطابق للعملاء والموردين
        self.setWindowTitle("📦 تعديل عنصر المخزون - نظام إدارة المخزون المتطور والشامل" if self.item else "📦 إضافة عنصر جديد للمخزون - نظام إدارة المخزون المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان - مطابق للعملاء والموردين
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان - مطابق للعملاء والموردين
        self.customize_title_bar()

        self.setModal(True)
        self.resize(650, 650)  # جعل النافذة مربعة مطابق للعملاء والموردين

        # خلفية النافذة مطابقة تماماً للعملاء والموردين
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # إنشاء التخطيط الرئيسي مطابق للعملاء والموردين
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)

        # إضافة عنوان النافذة الداخلي مطابق للعملاء والموردين
        title_text = "تعديل بيانات عنصر المخزون" if self.item else "إضافة عنصر جديد للمخزون"
        title_label = QLabel(f"📦 {title_text}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 18px 25px;
                margin: 8px 5px;
                font-weight: bold;
                font-size: 18px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4),
                           0 3px 12px rgba(37, 99, 235, 0.3);
                min-height: 50px;
                max-height: 50px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء نموذج معلومات العنصر مطابق للعملاء والموردين
        from ui.unified_styles import StyledGroupBox
        form_group = StyledGroupBox("📦 معلومات العنصر")

        # تخطيط النموذج مطابق للعملاء والموردين
        form_layout = QFormLayout()

        # دالة إنشاء تسمية مصممة مطابقة للعملاء والموردين
        def create_styled_label(text, icon="", required=False):
            label_text = f"{icon} {text}" if icon else text
            if required:
                label_text += " *"

            label = QLabel(label_text)
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    padding: 8px 12px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(37, 99, 235, 0.8), stop:0.5 rgba(59, 130, 246, 0.9), stop:1 rgba(96, 165, 250, 0.8));
                    border: 2px solid rgba(37, 99, 235, 0.6);
                    border-radius: 8px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
                    min-width: 140px;
                    max-width: 140px;
                }
            """)
            return label

        # حقل اسم العنصر مطابق للعملاء والموردين
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("📦 أدخل اسم العنصر...")
        self.name_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QLineEdit:hover {
                border: 3px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.98),
                    stop:0.2 rgba(241, 245, 249, 0.95),
                    stop:0.4 rgba(226, 232, 240, 0.9),
                    stop:0.6 rgba(241, 245, 249, 0.95),
                    stop:0.8 rgba(250, 251, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.name_edit.setText(self.item.name)
        form_layout.addRow(create_styled_label("اسم العنصر", "📦", True), self.name_edit)

        # حقل الفئة مطابق للعملاء والموردين
        self.category_combo = QComboBox()
        self.category_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        # خيارات متعددة للفئات مطابقة للمشاريع والعقارات
        categories = [
            "دهانات", "سيراميك", "أخشاب", "أدوات صحية", "أدوات كهربائية",
            "مواد بناء", "حديد وصلب", "أسمنت", "رمل وحصى", "بلاط",
            "أنابيب", "مواسير", "عوازل", "زجاج", "أقفال", "مفاتيح",
            "إضاءة", "تكييف", "أجهزة", "أدوات", "أخرى"
        ]
        for category in categories:
            self.category_combo.addItem(category)
        if self.item and self.item.category:
            index = self.category_combo.findText(self.item.category)
            if index >= 0:
                self.category_combo.setCurrentIndex(index)
        form_layout.addRow(create_styled_label("الفئة", "🏷️"), self.category_combo)

        # حقل وحدة القياس مطابق للعملاء والموردين
        self.unit_combo = QComboBox()
        self.unit_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        # خيارات متعددة لوحدات القياس مطابقة للمشاريع والعقارات
        units = [
            "قطعة", "متر", "متر مربع", "متر مكعب", "كيلوجرام", "جرام", "طن",
            "لتر", "مليلتر", "علبة", "كرتون", "لوح", "رول", "كيس", "زجاجة",
            "عبوة", "حزمة", "دستة", "مجموعة", "وحدة"
        ]
        for unit in units:
            self.unit_combo.addItem(unit)
        if self.item and self.item.unit:
            index = self.unit_combo.findText(self.item.unit)
            if index >= 0:
                self.unit_combo.setCurrentIndex(index)
        form_layout.addRow(create_styled_label("وحدة القياس", "📏"), self.unit_combo)

        # حقل الكمية مطابق للعملاء والموردين
        self.quantity_edit = QDoubleSpinBox()
        self.quantity_edit.setRange(0, 100000)
        self.quantity_edit.setDecimals(0)
        self.quantity_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QDoubleSpinBox:hover {
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.quantity_edit.setValue(self.item.quantity or 0)
        form_layout.addRow(create_styled_label("الكمية", "📊"), self.quantity_edit)

        # حقل الحد الأدنى مطابق للعملاء والموردين
        self.min_quantity_edit = QDoubleSpinBox()
        self.min_quantity_edit.setRange(0, 10000)
        self.min_quantity_edit.setDecimals(0)
        self.min_quantity_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(239, 68, 68, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(239, 68, 68, 0.3);
                box-shadow: 0 4px 15px rgba(239, 68, 68, 0.25);
            }
            QDoubleSpinBox:hover {
                border: 3px solid rgba(239, 68, 68, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(239, 68, 68, 0.3);
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(239, 68, 68, 0.95);
                box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.min_quantity_edit.setValue(self.item.min_quantity or 0)
        form_layout.addRow(create_styled_label("الحد الأدنى", "⚠️"), self.min_quantity_edit)

        # حقل سعر التكلفة مطابق للعملاء والموردين
        self.cost_price_edit = QDoubleSpinBox()
        self.cost_price_edit.setRange(0, 1000000)
        self.cost_price_edit.setDecimals(0)
        self.cost_price_edit.setSingleStep(10)
        self.cost_price_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(245, 158, 11, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(245, 158, 11, 0.3);
                box-shadow: 0 4px 15px rgba(245, 158, 11, 0.25);
            }
            QDoubleSpinBox:hover {
                border: 3px solid rgba(245, 158, 11, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(245, 158, 11, 0.95);
                box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.cost_price_edit.setValue(self.item.cost_price or 0)
        form_layout.addRow(create_styled_label("سعر التكلفة", "💰"), self.cost_price_edit)

        # حقل سعر البيع مطابق للعملاء والموردين
        self.selling_price_edit = QDoubleSpinBox()
        self.selling_price_edit.setRange(0, 1000000)
        self.selling_price_edit.setDecimals(0)
        self.selling_price_edit.setSingleStep(10)
        self.selling_price_edit.setStyleSheet("""
            QDoubleSpinBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(16, 185, 129, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(16, 185, 129, 0.3);
                box-shadow: 0 4px 15px rgba(16, 185, 129, 0.25);
            }
            QDoubleSpinBox:hover {
                border: 3px solid rgba(16, 185, 129, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
            }
            QDoubleSpinBox:focus {
                border: 4px solid rgba(16, 185, 129, 0.95);
                box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.selling_price_edit.setValue(self.item.selling_price or 0)
        form_layout.addRow(create_styled_label("سعر البيع", "💵", True), self.selling_price_edit)

        # حقل المورد مطابق للعملاء والموردين
        self.supplier_combo = QComboBox()
        self.supplier_combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(147, 51, 234, 0.3);
                box-shadow: 0 4px 15px rgba(147, 51, 234, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(147, 51, 234, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
                transform: scale(1.02);
            }
        """)
        self.supplier_combo.addItem("-- اختر مورد --", None)
        if self.session:
            suppliers = self.session.query(Supplier).all()
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier.name, supplier.id)
        if self.item and self.item.supplier_id:
            index = self.supplier_combo.findData(self.item.supplier_id)
            if index >= 0:
                self.supplier_combo.setCurrentIndex(index)
        form_layout.addRow(create_styled_label("المورد", "🚛"), self.supplier_combo)

        # حقل موقع التخزين مطابق للعملاء والموردين
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("📍 أدخل موقع التخزين...")
        self.location_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 10px;
                padding: 14px 18px;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QLineEdit:hover {
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)
        if self.item:
            self.location_edit.setText(self.item.location or "")
        form_layout.addRow(create_styled_label("موقع التخزين", "📍"), self.location_edit)

        form_group.setLayout(form_layout)

        # أزرار التحكم - مطابقة تماماً للعملاء والموردين
        buttons_layout = QHBoxLayout()

        # زر الحفظ مطابق للعملاء والموردين
        save_button = QPushButton("💾 حفظ")
        self.style_advanced_button(save_button, 'emerald')
        save_button.clicked.connect(self.accept)

        # زر الإلغاء مطابق للعملاء والموردين
        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'danger')
        cancel_button.clicked.connect(self.reject)

        # إضافة الأزرار بنفس ترتيب العملاء والموردين
        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(save_button)

        # تجميع التخطيط النهائي
        main_layout.addWidget(form_group.group_box)
        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)

    def get_data(self):
        """الحصول على بيانات عنصر المخزون من النموذج"""
        name = self.name_edit.text().strip()
        category = self.category_combo.currentText()
        unit = self.unit_combo.currentText()
        quantity = self.quantity_edit.value()
        min_quantity = self.min_quantity_edit.value()
        cost_price = self.cost_price_edit.value()
        selling_price = self.selling_price_edit.value()
        supplier_id = self.supplier_combo.currentData()
        location = self.location_edit.text().strip()

        # التحقق من صحة البيانات
        if not name:
            show_error_message("خطأ", "يجب إدخال اسم العنصر")
            return None

        if selling_price < cost_price:
            if not show_confirmation_message("تحذير", "سعر البيع أقل من سعر التكلفة. هل تريد المتابعة؟"):
                return None

        return {
            'name': name,
            'category': category,
            'unit': unit,
            'quantity': quantity,
            'min_quantity': min_quantity,
            'cost_price': cost_price,
            'selling_price': selling_price,
            'supplier_id': supplier_id,
            'location': location,
            'notes': '',  # إزالة الملاحظات
            'last_updated': datetime.datetime.now()
        }

class InventoryMainWidget(QWidget):
    """واجهة إدارة المخزون الرئيسية مع تبويبات"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        print("🔧 بدء إنشاء واجهة المخازن...")
        try:
            self.init_ui()
            print("✅ تم إنشاء واجهة المخازن بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء واجهة المخازن: {str(e)}")
            self.create_emergency_ui()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للعمال مع تقليل المساحات الفارغة لاستغلال المساحة للجداول
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من جميع الاتجاهات لاستغلال المساحة
        main_layout.setSpacing(2)  # تقليل المسافات بين العناصر

        # إنشاء تبويبات للمخزون والمشتريات مع تنسيق مطابق للمشاريع والعمال
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 8px;
                background: #ffffff;
                margin-top: -1px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border: 4px solid #000000;
                border-bottom: 4px solid #000000;
                border-radius: 12px;
                padding: 8px 32px;
                margin: 2px;
                font-size: 20px;
                font-weight: bold;
                min-width: 562px;
                max-width: 562px;
                min-height: 30px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2),
                           0 1px 3px rgba(0, 0, 0, 0.1),
                           0 -2px 5px rgba(0, 0, 0, 0.1);
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 6px solid #2563EB;
                border-bottom: 6px solid #2563EB;
                margin-top: -1px;
                padding: 9px 32px;
                font-size: 20px;
                font-weight: bold;
                min-width: 560px;
                max-width: 560px;
                min-height: 30px;
                max-height: 40px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4),
                           0 3px 12px rgba(0, 0, 0, 0.3),
                           0 -3px 8px rgba(37, 99, 235, 0.3);
                border-radius: 12px;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.2 #1e293b, stop:0.3 #0f172a,
                    stop:0.4 #1e40af, stop:0.6 #1d4ed8, stop:0.7 #0f172a,
                    stop:0.8 #1e293b, stop:1 #334155);
                border: 4px solid #3B82F6;
                border-bottom: 4px solid #3B82F6;
                color: #ffffff;
                font-weight: 800;
                font-size: 20px;
                min-width: 562px;
                max-width: 562px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.45);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35),
                           0 2px 6px rgba(0, 0, 0, 0.2),
                           0 -2px 5px rgba(59, 130, 246, 0.25);
                border-radius: 12px;
            }
        """)

        # إنشاء تبويب المخزون مع أيقونة مثل المشاريع
        print("🔧 إنشاء تبويب المخزون...")
        try:
            self.inventory_widget = InventoryWidget(self.session)
            self.tabs.addTab(self.inventory_widget, "📦 إدارة المخزون")
            print("✅ تم إنشاء تبويب المخزون بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء تبويب المخزون: {str(e)}")
            # إنشاء تبويب بسيط للمخزون
            simple_inventory = self.create_simple_inventory_widget()
            self.tabs.addTab(simple_inventory, "📦 إدارة المخزون")

        # إنشاء تبويب المشتريات مع أيقونة مثل المشاريع
        print("🔧 إنشاء تبويب المشتريات...")
        try:
            from ui.purchases import PurchasesWidget
            self.purchases_widget = PurchasesWidget(self.session)
            self.tabs.addTab(self.purchases_widget, "🛒 إدارة المشتريات")
            print("✅ تم تحميل تبويب المشتريات المتطور بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تحميل المشتريات المتطورة: {str(e)}")
            import traceback
            traceback.print_exc()
            # إضافة تبويب بسيط للمشتريات
            self.purchases_widget = self.create_simple_purchases_widget()
            self.tabs.addTab(self.purchases_widget, "🛒 إدارة المشتريات")
            print("✅ تم إنشاء تبويب المشتريات البسيط بنجاح")

        # إنشاء تبويب المبيعات مع أيقونة مثل المشاريع
        print("🔧 إنشاء تبويب المبيعات...")
        try:
            from ui.sales import SalesWidget
            self.sales_widget = SalesWidget(self.session)
            self.tabs.addTab(self.sales_widget, "💰 إدارة المبيعات")
            print("✅ تم تحميل تبويب المبيعات المتطور بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تحميل المبيعات المتطورة: {str(e)}")
            # إضافة تبويب بسيط للمبيعات
            self.sales_widget = self.create_simple_sales_widget()
            self.tabs.addTab(self.sales_widget, "💰 إدارة المبيعات")
            print("✅ تم إنشاء تبويب المبيعات البسيط بنجاح")

        # إضافة التبويبات إلى التخطيط الرئيسي
        main_layout.addWidget(self.tabs, 1)  # إعطاء التبويبات أولوية في التمدد

        self.setLayout(main_layout)

    def create_emergency_ui(self):
        """إنشاء واجهة طوارئ بسيطة"""
        print("🚨 إنشاء واجهة طوارئ للمخازن...")
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("🏪 إدارة المخازن")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #3b82f6;
                color: white;
                padding: 15px;
                border-radius: 10px;
                margin: 10px;
            }
        """)

        # رسالة
        message = QLabel("حدث خطأ في تحميل واجهة المخازن.\nسيتم إصلاح هذه المشكلة قريباً.")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #6b7280;
                padding: 30px;
                background-color: #f9fafb;
                border-radius: 8px;
                margin: 20px;
            }
        """)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addStretch()

        self.setLayout(layout)
        print("✅ تم إنشاء واجهة طوارئ للمخازن")

    def create_simple_inventory_widget(self):
        """إنشاء تبويب مخزون بسيط"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("📦 المخزون")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #059669;
                color: white;
                padding: 10px;
                border-radius: 8px;
                margin: 10px;
            }
        """)

        # رسالة
        message = QLabel("سيتم تطوير هذا القسم قريباً...")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6b7280;
                padding: 20px;
            }
        """)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def create_simple_purchases_widget(self):
        """إنشاء تبويب مشتريات بسيط"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("🛒 المشتريات")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #3b82f6;
                color: white;
                padding: 10px;
                border-radius: 8px;
                margin: 10px;
            }
        """)

        # رسالة
        message = QLabel("سيتم تطوير هذا القسم قريباً...")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6b7280;
                padding: 20px;
            }
        """)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def create_simple_sales_widget(self):
        """إنشاء تبويب مبيعات بسيط"""
        widget = QWidget()
        layout = QVBoxLayout()

        # عنوان
        title = QLabel("💰 المبيعات")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #10b981;
                color: white;
                padding: 10px;
                border-radius: 8px;
                margin: 10px;
            }
        """)

        # رسالة
        message = QLabel("سيتم تطوير هذا القسم قريباً...")
        message.setAlignment(Qt.AlignCenter)
        message.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6b7280;
                padding: 20px;
            }
        """)

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

class InventoryWidget(QWidget):
    """واجهة إدارة المخزون"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()
        # تأجيل تحميل البيانات لتحسين الأداء
        QTimer.singleShot(350, self.load_data_safely)

    def load_data_safely(self):
        """تحميل البيانات بشكل آمن"""
        try:
            self.refresh_data()
        except Exception as e:
            print(f"❌ خطأ في تحديث بيانات المخزون: {str(e)}")
            # إنشاء بيانات تجريبية بسيطة
            self.create_sample_data()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للموردين والعمال والمصروفات والإيرادات والمشاريع والفواتير
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من جميع الاتجاهات لاستغلال المساحة
        main_layout.setSpacing(3)  # تقليل المسافات بين العناصر

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("📦 إدارة المخزون المتطورة - نظام شامل ومتقدم لإدارة المخزون مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث بالاسم، الفئة، المورد أو الموقع...")
        # سيتم ربط الأحداث في نهاية init_ui()
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 3px solid rgba(139, 92, 246, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(240, 249, 255, 0.9),
                    stop:1 rgba(224, 242, 254, 0.85));
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QLineEdit:hover {
                border: 3px solid rgba(124, 58, 237, 0.7);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
            }
        """)

        self.search_button = QPushButton("🔍")
        self.search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                font-size: 20px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
                box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(91, 33, 182, 0.9),
                    stop:1 rgba(76, 29, 149, 0.8));
                border: 3px solid rgba(91, 33, 182, 0.9);
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
            }
        """)
        # سيتم ربط الأحداث في نهاية init_ui()
        self.search_button.setToolTip("بحث متقدم")
        self.search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        self.search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة مطابقة للفواتير
        filter_label = QLabel("🎯 فئة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة مطابقة للفواتير
        self.create_custom_category_filter()


        # تسمية المخزون المنخفض مطابقة للفواتير
        stock_label = QLabel("📉 مخزون:")
        stock_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)
        stock_label.setAlignment(Qt.AlignCenter)

        # إنشاء قائمة تصفية مخصصة للمخزون المنخفض مطابقة للفواتير
        self.create_custom_stock_filter()


        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(self.search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.category_filter_frame, 1, Qt.AlignVCenter)
        search_layout.addWidget(stock_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.stock_filter_frame, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول المخزون المتطور والمحسن
        self.create_advanced_inventory_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.inventory_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين

        # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة
        self.add_button = QPushButton("➕ إضافة عنصر")
        self.style_advanced_button(self.add_button, 'emerald', has_menu=True)  # أخضر زمردي مميز مع قائمة
        self.add_button.clicked.connect(self.add_item)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')  # أزرق سماوي متطور مطابق للفواتير
        self.edit_button.clicked.connect(self.edit_item)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')  # أحمر تحذيري
        self.delete_button.clicked.connect(self.delete_item)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')  # تصميم حديث ومتطور
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # المجموعة الثانية - العمليات المتقدمة
        self.view_button = QPushButton("👁️ عرض التفاصيل ▼")
        self.style_advanced_button(self.view_button, 'indigo', has_menu=True)  # بنفسجي للتفاصيل
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة لعرض التفاصيل مطابقة للأقسام الأخرى
        from ui.unified_styles import UnifiedStyles
        view_menu = QMenu(self)
        view_menu.setStyleSheet(UnifiedStyles.get_menu_style('indigo', 'normal'))

        view_details_action = QAction("👁️ عرض التفاصيل", self)
        view_details_action.triggered.connect(self.view_item)
        view_menu.addAction(view_details_action)

        stock_history_action = QAction("📊 تاريخ المخزون", self)
        stock_history_action.triggered.connect(self.view_stock_history)
        view_menu.addAction(stock_history_action)

        supplier_info_action = QAction("🏪 معلومات المورد", self)
        supplier_info_action.triggered.connect(self.view_supplier_info)
        view_menu.addAction(supplier_info_action)

        stock_alerts_action = QAction("⚠️ تنبيهات المخزون", self)
        stock_alerts_action.triggered.connect(self.view_stock_alerts)
        view_menu.addAction(stock_alerts_action)

        self.view_button.setMenu(view_menu)

        self.adjust_button = QPushButton("📊 تعديل الكمية")
        self.style_advanced_button(self.adjust_button, 'orange')  # برتقالي للكميات
        self.adjust_button.clicked.connect(self.adjust_quantity)
        self.adjust_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'info', has_menu=True)  # لون متسق مع نظام الألوان
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة منسدلة للتصدير مطابقة للأقسام الأخرى
        export_menu = QMenu(self)
        export_menu.setStyleSheet(UnifiedStyles.get_menu_style('info', 'normal'))

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(self.export_to_excel)
        export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(self.export_to_csv)
        export_menu.addAction(csv_action)

        json_action = QAction("🔗 تصدير إلى JSON", self)
        json_action.triggered.connect(self.export_to_json)
        export_menu.addAction(json_action)

        self.export_button.setMenu(export_menu)

        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')  # وردي للإحصائيات
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إجمالي المخزون مطور ليتشابه مع الفواتير
        self.total_label = QLabel("إجمالي العناصر: 0")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.adjust_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        main_layout.addWidget(bottom_frame)

        # ربط الأحداث في النهاية بعد إنشاء جميع العناصر
        self.connect_events()

        self.setLayout(main_layout)

    def connect_events(self):
        """ربط جميع الأحداث بعد إنشاء جميع العناصر"""
        try:
            # ربط حدث البحث
            self.search_edit.textChanged.connect(self.filter_inventory)
            # ربط زر البحث
            self.search_button.clicked.connect(self.filter_inventory)
            print("✅ تم ربط أحداث المخزون بنجاح")
        except Exception as e:
            print(f"❌ خطأ في ربط أحداث المخزون: {str(e)}")

    def create_advanced_inventory_table(self):
        """إنشاء جدول المخزون المتطور والمحسن مطابق للموردين"""
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(9)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🔢 ID",
            "📦 اسم العنصر",
            "🏷️ الفئة",
            "📊 الكمية",
            "📏 الوحدة",
            "⚠️ الحد الأدنى",
            "💰 سعر التكلفة",
            "💵 سعر البيع",
            "🏢 المورد"
        ]
        self.inventory_table.setHorizontalHeaderLabels(headers)

        # إعدادات عرض الأعمدة مع التكيف التلقائي مطابقة للعملاء
        header = self.inventory_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # ID
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # اسم العنصر
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # الفئة
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # الكمية
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # الوحدة
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # الحد الأدنى
        header.setSectionResizeMode(6, QHeaderView.Stretch)  # سعر التكلفة
        header.setSectionResizeMode(7, QHeaderView.Stretch)  # سعر البيع
        header.setSectionResizeMode(8, QHeaderView.Stretch)  # المورد

        # تحديد عرض الأعمدة الثابتة مطابق للعملاء
        self.inventory_table.setColumnWidth(0, 120)  # ID
        self.inventory_table.setColumnWidth(1, 300)  # اسم العنصر

        self.inventory_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.inventory_table.setSelectionMode(QTableWidget.SingleSelection)
        self.inventory_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.inventory_table.setAlternatingRowColors(True)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.inventory_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.inventory_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)  # ارتفاع الصف الواحد
                scrollbar.setPageStep(200)   # 4 صفوف للصفحة
        except Exception:
            pass

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للمصروفات والإيرادات والفواتير
        self.inventory_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }

            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                /* color: #1e293b; */ /* تم إزالة اللون الثابت للسماح بألوان مخصصة */
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9),
                    stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9),
                    stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9),
                    stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-top: 3px solid rgba(255, 255, 255, 0.7) !important;
                border-bottom: 5px solid rgba(255, 255, 255, 0.8) !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15),
                    stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25),
                    stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-top: 2px solid rgba(102, 126, 234, 0.5) !important;
                border-bottom: 4px solid rgba(102, 126, 234, 0.6) !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للمصروفات والإيرادات والفواتير
        header = self.inventory_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                vertical-align: middle !important;
                line-height: 31px !important;
                letter-spacing: 1.3px !important;
                position: relative !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #475569,
                    stop:0.3 #64748B, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5),
                           inset 0 2px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -1px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-bottom: 5px solid #d4d4aa !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9),
                           1px 1px 2px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.5px !important;
                font-size: 15px !important;
                color: #ffffff !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #1E40AF, stop:0.5 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.7 #5B21B6, stop:0.8 #4C1D95,
                    stop:0.9 #3730A3, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6),
                           0 2px 4px rgba(0, 0, 0, 0.4),
                           inset 0 0 12px rgba(0, 0, 0, 0.4) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                border-top: 2px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9),
                           0px 0px 1px rgba(0, 0, 0, 0.7) !important;
                letter-spacing: 1.2px !important;
                color: #f0f0f0 !important;
                font-weight: 900 !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للمصروفات والإيرادات والفواتير
        self.inventory_table.verticalHeader().setDefaultSectionSize(45)
        self.inventory_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        # إضافة العلامة المائية للجدول مطابقة للفواتير
        self.add_watermark_to_inventory_table()

        # إضافة معالج التمرير المخصص (يحاكي سلوك الأسهم)
        def wheelEvent(event):
            try:
                # التمرير العمودي بالماوس
                delta = event.angleDelta().y()

                # تجاهل الحركات الصغيرة جداً
                if abs(delta) < 120:
                    event.accept()
                    return

                # الحصول على شريط التمرير
                scrollbar = self.inventory_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                # محاكاة سلوك الأسهم - خطوة واحدة في كل مرة
                if delta > 0:
                    # التمرير لأعلى - مثل الضغط على السهم العلوي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    # التمرير لأسفل - مثل الضغط على السهم السفلي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()

            except Exception:
                # في حالة الخطأ، استخدم التمرير الافتراضي
                QTableWidget.wheelEvent(self.inventory_table, event)

        self.inventory_table.wheelEvent = wheelEvent

    def add_watermark_to_inventory_table(self):
        """إضافة علامة مائية للجدول مطابقة للعملاء"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")
            painter.restore()

        original_paint = self.inventory_table.paintEvent
        def new_paint_event(event):
            try:
                original_paint(event)
                painter = QPainter(self.inventory_table.viewport())
                paint_watermark(painter, self.inventory_table.viewport().rect())
                painter.end()
            except Exception:
                pass

        self.inventory_table.paintEvent = new_paint_event
        # إجبار إعادة الرسم
        self.inventory_table.viewport().update()
        self.inventory_table.repaint()

    def refresh_data(self):
        """تحديث بيانات المخزون في الجدول"""
        # الحصول على جميع عناصر المخزون من قاعدة البيانات
        inventory_items = self.session.query(Inventory).order_by(Inventory.name).all()

        # إذا لم توجد بيانات، إنشاء بيانات تجريبية
        if not inventory_items:
            print("🧪 لا توجد بيانات في المخزون، إنشاء بيانات تجريبية...")
            self.create_sample_inventory_data()
            inventory_items = self.session.query(Inventory).order_by(Inventory.name).all()

        self.populate_table(inventory_items)
        self.update_summary(inventory_items)

    def populate_table(self, items):
        """ملء جدول المخزون بالبيانات"""
        self.inventory_table.setRowCount(0)

        for row, item in enumerate(items):
            self.inventory_table.insertRow(row)

            # دالة مساعدة لإنشاء العناصر مطابق للعملاء
            def create_item(icon, text, default="No Data"):
                display_text = text if text and text.strip() else default
                item = QTableWidgetItem(f"{icon} {display_text}")
                item.setTextAlignment(Qt.AlignCenter)
                if display_text == default:
                    item.setForeground(QColor("#ef4444"))
                return item

            # الرقم مع أيقونة ثابتة - لون أسود للأرقام مطابق للعملاء
            id_item = QTableWidgetItem(f"🔢 {item.id}")
            id_item.setTextAlignment(Qt.AlignCenter)
            id_item.setForeground(QColor("#000000"))  # لون أسود للرقم مطابق للعملاء
            self.inventory_table.setItem(row, 0, id_item)

            self.inventory_table.setItem(row, 1, create_item("📦", item.name))

            # الفئة مطابق للعنوان
            self.inventory_table.setItem(row, 2, create_item("🏷️", item.category))

            # باقي الأعمدة مطابق للعملاء
            from utils import format_quantity, format_currency

            quantity_text = format_quantity(item.quantity) if item.quantity else None
            unit_text = item.unit or None
            min_quantity_text = format_quantity(item.min_quantity) if item.min_quantity else None
            cost_price_text = format_currency(item.cost_price) if item.cost_price else None

            # الكمية والحد الأدنى مطابق للعناوين
            self.inventory_table.setItem(row, 3, create_item("📊", quantity_text))
            self.inventory_table.setItem(row, 4, create_item("📏", unit_text))
            self.inventory_table.setItem(row, 5, create_item("⚠️", min_quantity_text))
            self.inventory_table.setItem(row, 6, create_item("💰", cost_price_text))

            # باقي الأعمدة مطابق للعملاء
            selling_price_text = format_currency(item.selling_price) if item.selling_price else None
            supplier_name = item.supplier.name if item.supplier else None

            self.inventory_table.setItem(row, 7, create_item("💵", selling_price_text))
            self.inventory_table.setItem(row, 8, create_item("🏢", supplier_name))

    def update_summary(self, items):
        """تحديث ملخص المخزون"""
        total_items = len(items)
        low_stock_items = sum(1 for item in items if item.quantity <= item.min_quantity)
        total_value = sum(item.quantity * item.cost_price for item in items)

        self.total_label.setText(f"إجمالي العناصر: {total_items} | منخفض المخزون: {low_stock_items} | القيمة: {format_currency(total_value)}")

    def filter_inventory(self):
        """تصفية المخزون بناءً على نص البحث والفئة وحالة المخزون"""
        try:
            search_text = self.search_edit.text().strip().lower()
            category = getattr(self, 'current_category_value', 'all')
            low_stock = getattr(self, 'current_stock_value', 'all')
        except Exception as e:
            print(f"خطأ في الحصول على قيم التصفية: {str(e)}")
            return

        try:
            # بناء الاستعلام
            query = self.session.query(Inventory)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Inventory.name.like(f"%{search_text}%") |
                    Inventory.location.like(f"%{search_text}%")
                )

            # تطبيق تصفية الفئة
            if category and category != "all":
                query = query.filter(Inventory.category == category)

            # تطبيق تصفية المخزون المنخفض
            if low_stock == "low":
                query = query.filter(Inventory.quantity <= Inventory.min_quantity)

            # تنفيذ الاستعلام
            items = query.order_by(Inventory.name).all()

            # تحديث الجدول والملخص
            self.populate_table(items)
            self.update_summary(items)

        except Exception as e:
            print(f"خطأ في تصفية المخزون: {str(e)}")
            # في حالة الخطأ، عرض جميع العناصر
            try:
                items = self.session.query(Inventory).order_by(Inventory.name).all()
                self.populate_table(items)
                self.update_summary(items)
            except Exception as e2:
                print(f"خطأ في عرض جميع العناصر: {str(e2)}")
                # إنشاء جدول فارغ
                self.inventory_table.setRowCount(0)
                if hasattr(self, 'total_label'):
                    self.total_label.setText("إجمالي العناصر: 0")

    def add_item(self):
        """إضافة عنصر جديد للمخزون"""
        dialog = InventoryItemDialog(self, session=self.session)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                # إنشاء عنصر جديد
                item = Inventory(**data)
                self.session.add(item)
                self.session.commit()
                show_info_message("تم", "تم إضافة العنصر بنجاح")
                self.refresh_data()

    def edit_item(self):
        """تعديل عنصر"""
        from utils import safe_edit_item
        from database import Inventory
        safe_edit_item(self, self.inventory_table, Inventory, InventoryItemDialog, self.session, "عنصر")
    def delete_item(self):
        """حذف عنصر"""
        from utils import safe_delete_item
        from database import Inventory
        safe_delete_item(self, self.inventory_table, Inventory, self.session, "عنصر", "name")
    def view_item(self):
        """عرض تفاصيل عنصر المخزون"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
            return

        item_id = int(self.inventory_table.item(selected_row, 0).text())
        item = self.session.query(Inventory).get(item_id)

        if not item:
            show_error_message("خطأ", "لم يتم العثور على العنصر")
            return

        # إنشاء نافذة لعرض تفاصيل العنصر
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تفاصيل العنصر: {item.name}")
        dialog.setMinimumSize(500, 300)

        layout = QVBoxLayout()

        # عنوان العنصر
        title_label = QLabel(item.name)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title_label)

        # معلومات العنصر
        info_layout = QFormLayout()

        info_layout.addRow("الفئة:", QLabel(item.category or ""))

        unit_text = f"{format_quantity(item.quantity)} {item.unit}" if item.unit else format_quantity(item.quantity)
        info_layout.addRow("الكمية:", QLabel(unit_text))

        info_layout.addRow("الحد الأدنى:", QLabel(format_quantity(item.min_quantity)))

        info_layout.addRow("سعر التكلفة:", QLabel(format_currency(item.cost_price)))

        info_layout.addRow("سعر البيع:", QLabel(format_currency(item.selling_price)))

        supplier_name = item.supplier.name if item.supplier else ""
        info_layout.addRow("المورد:", QLabel(supplier_name))

        info_layout.addRow("موقع التخزين:", QLabel(item.location or ""))

        last_updated = item.last_updated.strftime("%Y-%m-%d %H:%M") if item.last_updated else ""
        info_layout.addRow("آخر تحديث:", QLabel(last_updated))

        layout.addLayout(info_layout)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)
        dialog.setLayout(layout)

        dialog.exec_()

    def adjust_quantity(self):
        """تعديل كمية عنصر في المخزون"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
            return

        item_id = int(self.inventory_table.item(selected_row, 0).text())
        item = self.session.query(Inventory).get(item_id)

        if not item:
            show_error_message("خطأ", "لم يتم العثور على العنصر")
            return

        # إنشاء نافذة لتعديل الكمية
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تعديل كمية العنصر: {item.name}")
        dialog.setMinimumWidth(300)

        layout = QVBoxLayout()

        # معلومات العنصر
        info_label = QLabel(f"العنصر: {item.name}")
        layout.addWidget(info_label)

        current_quantity_label = QLabel(f"الكمية الحالية: {item.quantity} {item.unit}")
        layout.addWidget(current_quantity_label)

        # نموذج تعديل الكمية
        form_layout = QFormLayout()

        # حقل الكمية الجديدة
        self.new_quantity_edit = QDoubleSpinBox()
        self.new_quantity_edit.setRange(0, 100000)
        self.new_quantity_edit.setDecimals(0)  # بدون كسور عشرية
        self.new_quantity_edit.setValue(item.quantity)
        form_layout.addRow("الكمية الجديدة:", self.new_quantity_edit)

        layout.addLayout(form_layout)

        # أزرار الحفظ والإلغاء
        button_layout = QHBoxLayout()

        save_button = QPushButton("حفظ")
        save_button.clicked.connect(lambda: self.save_quantity_adjustment(dialog, item))
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #10b981;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)

        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(dialog.reject)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)

        button_layout.addStretch()
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)
        dialog.setLayout(layout)

        dialog.exec_()

    def save_quantity_adjustment(self, dialog, item):
        """حفظ تعديل كمية العنصر"""
        new_quantity = self.new_quantity_edit.value()

        # تحديث كمية العنصر
        item.quantity = new_quantity
        item.last_updated = datetime.datetime.now()

        self.session.commit()
        show_info_message("تم", f"تم تحديث كمية العنصر '{item.name}' بنجاح")
        self.refresh_data()
        dialog.accept()

    def export_to_excel(self):
        """تصدير بيانات المخزون إلى Excel"""
        self.export_to_csv()  # نفس الوظيفة

    def export_to_csv(self):
        """تصدير بيانات المخزون إلى CSV"""
        try:
            import csv
            from PyQt5.QtWidgets import QFileDialog

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف CSV", "قائمة_المخزون.csv", "ملفات CSV (*.csv)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.inventory_table.columnCount()):
                headers.append(self.inventory_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.inventory_table.rowCount()):
                row_data = []
                for col in range(self.inventory_table.columnCount()):
                    item = self.inventory_table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # كتابة البيانات إلى ملف CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(data)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def export_to_pdf(self):
        """تصدير بيانات المخزون إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument
            from PyQt5.QtWidgets import QFileDialog

            items = self.session.query(Inventory).all()

            if not items:
                show_info_message("تصدير PDF", "لا توجد عناصر للتصدير")
                return

            # حفظ ملف PDF مباشرة
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المخزون", "تقرير_المخزون.pdf", "PDF Files (*.pdf)"
            )

            if file_path:
                # إنشاء محتوى HTML
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <title>تقرير المخزون</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        h1 {{ color: #6366f1; text-align: center; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                        th {{ background-color: #f2f2f2; }}
                    </style>
                </head>
                <body>
                    <h1>📦 تقرير المخزون</h1>
                    <p><strong>تاريخ التقرير:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>

                    <table>
                        <tr>
                            <th>الرقم</th>
                            <th>اسم العنصر</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>القيمة الإجمالية</th>
                            <th>المورد</th>
                        </tr>
                """

                total_value = 0
                for item in items:
                    quantity = item.quantity or 0
                    cost_price = item.cost_price or 0
                    item_total = quantity * cost_price
                    total_value += item_total
                    supplier_name = item.supplier.name if item.supplier else "غير محدد"

                    html_content += f"""
                        <tr>
                            <td>{item.id}</td>
                            <td>{item.name}</td>
                            <td>{int(quantity):,}</td>
                            <td>{int(cost_price):,} جنيه</td>
                            <td>{int(item_total):,} جنيه</td>
                            <td>{supplier_name}</td>
                        </tr>
                    """

                html_content += f"""
                    </table>
                    <h3>إجمالي قيمة المخزون: {int(total_value):,} جنيه</h3>
                </body>
                </html>
                """

                # إنشاء طابعة PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

                # إنشاء مستند وطباعته إلى PDF
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                show_info_message("تم", f"تم تصدير المخزون إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير PDF: {str(e)}")

    def export_to_json(self):
        """تصدير بيانات المخزون إلى JSON"""
        try:
            import json
            from PyQt5.QtWidgets import QFileDialog

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف JSON", "قائمة_المخزون.json", "ملفات JSON (*.json)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.inventory_table.columnCount()):
                headers.append(self.inventory_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.inventory_table.rowCount()):
                row_data = {}
                for col in range(self.inventory_table.columnCount()):
                    item = self.inventory_table.item(row, col)
                    row_data[headers[col]] = item.text() if item else ""
                data.append(row_data)

            # كتابة البيانات إلى ملف JSON
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, ensure_ascii=False, indent=2)

            show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def view_stock_history(self):
        """عرض تاريخ المخزون للعنصر المحدد"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
            return

        item_id = int(self.inventory_table.item(selected_row, 0).text())
        item = self.session.query(Inventory).get(item_id)

        if not item:
            show_error_message("خطأ", "لم يتم العثور على العنصر")
            return

        # إنشاء نافذة لعرض تاريخ المخزون
        dialog = QDialog(self)
        dialog.setWindowTitle(f"تاريخ المخزون - {item.name}")
        dialog.setMinimumSize(600, 400)

        layout = QVBoxLayout()

        # معلومات العنصر
        info_text = f"""
📦 تاريخ المخزون - {item.name}

📊 المعلومات الحالية:
• الكمية الحالية: {item.quantity} {item.unit}
• الحد الأدنى: {item.min_quantity} {item.unit}
• سعر التكلفة: {format_currency(item.cost_price)}
• سعر البيع: {format_currency(item.selling_price)}
• آخر تحديث: {item.last_updated.strftime('%Y-%m-%d %H:%M') if item.last_updated else 'غير متوفر'}

📈 الإحصائيات:
• قيمة المخزون: {format_currency(item.quantity * item.cost_price)}
• الربح المتوقع: {format_currency(item.quantity * (item.selling_price - item.cost_price))}
• حالة المخزون: {'منخفض ⚠️' if item.quantity <= item.min_quantity else 'طبيعي ✅'}

📝 ملاحظات:
• يُنصح بإعادة الطلب عند الوصول للحد الأدنى
• تحقق من تواريخ انتهاء الصلاحية إن وجدت
        """

        info_label = QLabel(info_text)
        info_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;")
        layout.addWidget(info_label)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        layout.addWidget(close_button)

        dialog.setLayout(layout)
        dialog.exec_()

    def view_supplier_info(self):
        """عرض معلومات المورد للعنصر المحدد"""
        selected_row = self.inventory_table.currentRow()
        if selected_row < 0:
            show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة")
            return

        item_id = int(self.inventory_table.item(selected_row, 0).text())
        item = self.session.query(Inventory).get(item_id)

        if not item:
            show_error_message("خطأ", "لم يتم العثور على العنصر")
            return

        if not item.supplier:
            show_info_message("معلومات", f"لا يوجد مورد محدد للعنصر '{item.name}'")
            return

        supplier = item.supplier

        # إنشاء نافذة لعرض معلومات المورد
        dialog = QDialog(self)
        dialog.setWindowTitle(f"معلومات المورد - {supplier.name}")
        dialog.setMinimumSize(500, 350)

        layout = QVBoxLayout()

        # معلومات المورد
        supplier_text = f"""
🏪 معلومات المورد

📋 البيانات الأساسية:
• الاسم: {supplier.name}
• الهاتف: {supplier.phone or 'غير متوفر'}
• البريد الإلكتروني: {supplier.email or 'غير متوفر'}
• العنوان: {supplier.address or 'غير متوفر'}

💰 المعلومات المالية:
• الرصيد الحالي: {format_currency(supplier.balance)}
• حالة الرصيد: {'دائن' if supplier.balance > 0 else 'مدين' if supplier.balance < 0 else 'متوازن'}

📦 معلومات العنصر:
• اسم العنصر: {item.name}
• سعر التكلفة: {format_currency(item.cost_price)}
• الكمية المتوفرة: {item.quantity} {item.unit}

📝 ملاحظات:
{supplier.notes or 'لا توجد ملاحظات'}
        """

        supplier_label = QLabel(supplier_text)
        supplier_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;")
        layout.addWidget(supplier_label)

        # زر إغلاق
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(dialog.accept)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #6b7280;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #4b5563;
            }
        """)
        layout.addWidget(close_button)

        dialog.setLayout(layout)
        dialog.exec_()

    def export_low_stock_report(self):
        """تصدير تقرير المخزون المنخفض"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument

            # الحصول على العناصر منخفضة المخزون
            low_stock_items = self.session.query(Inventory).filter(
                Inventory.quantity <= Inventory.min_quantity
            ).all()

            if not low_stock_items:
                show_info_message("معلومات", "لا توجد عناصر منخفضة المخزون حالياً")
                return

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ تقرير المخزون المنخفض", "تقرير_المخزون_المنخفض.pdf", "ملفات PDF (*.pdf)")
            if not file_path:
                return

            # إنشاء طابعة PDF
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(file_path)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

            # إنشاء مستند نصي
            document = QTextDocument()

            # إنشاء محتوى HTML للتقرير
            html_content = self.generate_low_stock_report_html(low_stock_items)
            document.setHtml(html_content)

            # طباعة المستند إلى PDF
            document.print_(printer)

            show_info_message("تم", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")

    def generate_low_stock_report_html(self, low_stock_items):
        """إنشاء محتوى HTML لتقرير المخزون المنخفض"""
        try:
            html = f"""
            <html dir="rtl">
            <head>
                <meta charset="utf-8">
                <title>تقرير المخزون المنخفض</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #e74c3c; text-align: center; }}
                    h2 {{ color: #34495e; border-bottom: 2px solid #e74c3c; padding-bottom: 5px; }}
                    table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                    th {{ background-color: #e74c3c; color: white; }}
                    .warning {{ background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107; }}
                    .critical {{ background-color: #f8d7da; color: #721c24; }}
                </style>
            </head>
            <body>
                <h1>⚠️ تقرير المخزون المنخفض</h1>
                <p style="text-align: center;">تاريخ التقرير: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}</p>

                <div class="warning">
                    <h2>🚨 تحذير</h2>
                    <p>يوجد <strong>{len(low_stock_items)}</strong> عنصر منخفض المخزون يحتاج إلى إعادة طلب فوري!</p>
                </div>

                <h2>📋 تفاصيل العناصر المنخفضة</h2>
                <table>
                    <tr>
                        <th>اسم العنصر</th>
                        <th>الفئة</th>
                        <th>الكمية الحالية</th>
                        <th>الحد الأدنى</th>
                        <th>الوحدة</th>
                        <th>المورد</th>
                        <th>الحالة</th>
                    </tr>
            """

            # إضافة صفوف العناصر
            for item in low_stock_items:
                status_class = "critical" if item.quantity == 0 else ""
                status_text = "نفد المخزون" if item.quantity == 0 else "منخفض"

                html += f"""
                    <tr class="{status_class}">
                        <td>{item.name}</td>
                        <td>{item.category or ''}</td>
                        <td>{item.quantity}</td>
                        <td>{item.min_quantity}</td>
                        <td>{item.unit or ''}</td>
                        <td>{item.supplier.name if item.supplier else 'غير محدد'}</td>
                        <td>{status_text}</td>
                    </tr>
                """

            html += """
                </table>

                <div class="warning">
                    <h2>📝 توصيات</h2>
                    <ul>
                        <li>قم بإعادة طلب العناصر المنخفضة فوراً</li>
                        <li>تواصل مع الموردين لتأكيد توفر العناصر</li>
                        <li>راجع الحد الأدنى للمخزون بانتظام</li>
                        <li>فكر في زيادة الحد الأدنى للعناصر سريعة الاستهلاك</li>
                    </ul>
                </div>
            </body>
            </html>
            """

            return html
        except Exception as e:
            return f"""
            <html dir="rtl">
            <body>
                <h1>خطأ في إنشاء التقرير</h1>
                <p>حدث خطأ أثناء إنشاء تقرير المخزون المنخفض: {str(e)}</p>
            </body>
            </html>
            """

    def create_sample_inventory_data(self):
        """إنشاء بيانات تجريبية للمخزون"""
        try:
            # تحسين الأداء - إزالة الطباعة غير الضرورية
            # print("🧪 إنشاء بيانات تجريبية للمخزون...")

            sample_items = [
                {
                    'name': 'دهان أبيض داخلي',
                    'category': 'دهانات',
                    'unit': 'علبة',
                    'quantity': 50,
                    'min_quantity': 10,
                    'cost_price': 45,
                    'selling_price': 60,
                    'location': 'مخزن A - رف 1'
                },
                {
                    'name': 'سيراميك أرضي 60x60',
                    'category': 'سيراميك',
                    'unit': 'متر مربع',
                    'quantity': 200,
                    'min_quantity': 50,
                    'cost_price': 25,
                    'selling_price': 35,
                    'location': 'مخزن B - منطقة 2'
                },
                {
                    'name': 'خشب صنوبر 2x4',
                    'category': 'أخشاب',
                    'unit': 'لوح',
                    'quantity': 30,
                    'min_quantity': 5,
                    'cost_price': 80,
                    'selling_price': 120,
                    'location': 'مخزن C - منطقة الأخشاب'
                },
                {
                    'name': 'مغسلة حمام بيضاء',
                    'category': 'أدوات صحية',
                    'unit': 'قطعة',
                    'quantity': 15,
                    'min_quantity': 3,
                    'cost_price': 150,
                    'selling_price': 220,
                    'location': 'مخزن D - الأدوات الصحية'
                },
                {
                    'name': 'كابل كهربائي 2.5 مم',
                    'category': 'أدوات كهربائية',
                    'unit': 'متر',
                    'quantity': 500,
                    'min_quantity': 100,
                    'cost_price': 3,
                    'selling_price': 5,
                    'location': 'مخزن E - الكهربائيات'
                },
                {
                    'name': 'أسمنت بورتلاندي',
                    'category': 'مواد بناء',
                    'unit': 'كيس',
                    'quantity': 100,
                    'min_quantity': 20,
                    'cost_price': 18,
                    'selling_price': 25,
                    'location': 'مخزن F - مواد البناء'
                },
                {
                    'name': 'عنصر اختبار',
                    'category': None,  # فئة فارغة
                    'unit': None,      # وحدة فارغة
                    'quantity': 10,
                    'min_quantity': None,  # حد أدنى فارغ
                    'cost_price': None,    # سعر تكلفة فارغ
                    'selling_price': None, # سعر بيع فارغ
                    'location': None       # موقع فارغ
                }
            ]

            for item_data in sample_items:
                inventory_item = Inventory(
                    name=item_data['name'],
                    category=item_data['category'],
                    unit=item_data['unit'],
                    quantity=item_data['quantity'],
                    min_quantity=item_data['min_quantity'],
                    cost_price=item_data['cost_price'],
                    selling_price=item_data['selling_price'],
                    location=item_data['location'],
                    notes=f"عنصر تجريبي - {item_data['category'] or 'غير محدد'}"
                )
                self.session.add(inventory_item)

            self.session.commit()
            print(f"✅ تم إنشاء {len(sample_items)} عنصر تجريبي في المخزون")

        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات التجريبية: {str(e)}")
            self.session.rollback()



    def show_statistics(self):
        """عرض إحصائيات المخزون"""
        try:
            items = self.session.query(Inventory).all()

            if not items:
                show_info_message("إحصائيات المخزون", "لا توجد عناصر لعرض الإحصائيات")
                return

            # حساب الإحصائيات
            total_items = len(items)
            total_quantity = sum(item.quantity or 0 for item in items)
            total_value = sum((item.quantity or 0) * (item.cost_price or 0) for item in items)
            avg_price = sum(item.cost_price or 0 for item in items) / total_items if total_items > 0 else 0

            # إحصائيات المخزون المنخفض
            low_stock_items = [item for item in items if (item.quantity or 0) < (item.min_quantity or 5)]
            low_stock_count = len(low_stock_items)

            # إحصائيات حسب المورد
            supplier_stats = {}
            for item in items:
                supplier_name = item.supplier.name if item.supplier else 'غير محدد'
                if supplier_name in supplier_stats:
                    supplier_stats[supplier_name]['count'] += 1
                    supplier_stats[supplier_name]['value'] += (item.quantity or 0) * (item.cost_price or 0)
                else:
                    supplier_stats[supplier_name] = {
                        'count': 1,
                        'value': (item.quantity or 0) * (item.cost_price or 0)
                    }

            # إحصائيات حسب الفئة
            category_stats = {}
            for item in items:
                category = item.category or 'غير محدد'
                if category in category_stats:
                    category_stats[category] += 1
                else:
                    category_stats[category] = 1

            # إنشاء نافذة الإحصائيات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton, QHBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("📊 إحصائيات المخزون")
            dialog.setModal(True)
            dialog.resize(700, 600)

            layout = QVBoxLayout()

            # الإحصائيات العامة
            general_stats = f"""
📊 الإحصائيات العامة:
─────────────────────────────────────────────────────────────────────────────
📦 إجمالي العناصر: {total_items}
📊 إجمالي الكمية: {int(total_quantity):,}
💰 إجمالي القيمة: {int(total_value):,} جنيه
📈 متوسط السعر: {int(avg_price):,} جنيه

⚠️ تنبيهات المخزون:
─────────────────────────────────────────────────────────────────────────────
🔴 عناصر منخفضة المخزون: {low_stock_count} عنصر
"""

            if low_stock_items:
                general_stats += "العناصر المنخفضة:\n"
                for item in low_stock_items[:5]:  # أول 5 عناصر
                    general_stats += f"• {item.name}: {int(item.quantity or 0)} متبقي\n"

            general_stats += f"""
─────────────────────────────────────────────────────────────────────────────

🏪 توزيع حسب المورد:
─────────────────────────────────────────────────────────────────────────────
"""

            # أفضل 5 موردين
            sorted_suppliers = sorted(supplier_stats.items(), key=lambda x: x[1]['value'], reverse=True)[:5]
            for supplier, stats in sorted_suppliers:
                percentage = (stats['count'] / total_items) * 100
                general_stats += f"• {supplier}: {stats['count']} عنصر ({percentage:.1f}%) - {int(stats['value']):,} جنيه\n"

            general_stats += f"""
─────────────────────────────────────────────────────────────────────────────

📂 توزيع حسب الفئة:
─────────────────────────────────────────────────────────────────────────────
"""

            for category, count in category_stats.items():
                percentage = (count / total_items) * 100
                general_stats += f"• {category}: {count} عنصر ({percentage:.1f}%)\n"

            # عرض الإحصائيات
            stats_text = QTextBrowser()
            stats_text.setPlainText(general_stats)
            stats_text.setStyleSheet("""
                QTextBrowser {
                    font-family: 'Segoe UI', Arial, sans-serif;
                    font-size: 11px;
                    line-height: 1.4;
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
            """)
            layout.addWidget(stats_text)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            export_stats_btn = QPushButton("📤 تصدير الإحصائيات")
            export_stats_btn.clicked.connect(lambda: self.export_statistics_report(general_stats))
            buttons_layout.addWidget(export_stats_btn)

            low_stock_btn = QPushButton("⚠️ تقرير المخزون المنخفض")
            low_stock_btn.clicked.connect(lambda: self.export_low_stock_report())
            buttons_layout.addWidget(low_stock_btn)

            close_button = QPushButton("❌ إغلاق")
            close_button.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_button)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def export_statistics_report(self, stats_content):
        """تصدير تقرير الإحصائيات"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الإحصائيات", "إحصائيات_المخزون.txt", "Text Files (*.txt)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"""
═══════════════════════════════════════════════════════════════════════════════
                            📊 تقرير إحصائيات المخزون
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

{stats_content}

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المخزون
═══════════════════════════════════════════════════════════════════════════════
""")

                show_info_message("تم", f"تم تصدير تقرير الإحصائيات بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير التقرير: {str(e)}")

    def export_low_stock_report(self):
        """تصدير تقرير المخزون المنخفض"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            items = self.session.query(Inventory).all()
            low_stock_items = [item for item in items if (item.quantity or 0) < (item.min_quantity or 5)]

            if not low_stock_items:
                show_info_message("تقرير المخزون المنخفض", "لا توجد عناصر منخفضة المخزون")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المخزون المنخفض", "المخزون_المنخفض.txt", "Text Files (*.txt)"
            )

            if file_path:
                report_content = f"""
═══════════════════════════════════════════════════════════════════════════════
                            ⚠️ تقرير المخزون المنخفض
═══════════════════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {QDate.currentDate().toString('yyyy-MM-dd')}
⏰ وقت الإنشاء: {QDate.currentDate().toString('hh:mm:ss')}

🔴 عدد العناصر المنخفضة: {len(low_stock_items)}

📋 تفاصيل العناصر المنخفضة:
─────────────────────────────────────────────────────────────────────────────
"""

                for item in low_stock_items:
                    supplier_name = item.supplier.name if item.supplier else "غير محدد"
                    report_content += f"""
🔸 {item.name}
   📊 الكمية الحالية: {int(item.quantity or 0)}
   ⚠️ الحد الأدنى: {int(item.min_quantity or 5)}
   💰 سعر التكلفة: {int(item.cost_price or 0):,} جنيه
   🏪 المورد: {supplier_name}
   📂 الفئة: {item.category or 'غير محدد'}
   ─────────────────────────────────────────────────────────────────────────────
"""

                report_content += """
💡 التوصيات:
─────────────────────────────────────────────────────────────────────────────
• إعادة طلب العناصر المنخفضة فوراً
• مراجعة الحد الأدنى للمخزون
• التواصل مع الموردين لتأكيد التوفر
• مراقبة معدل الاستهلاك

═══════════════════════════════════════════════════════════════════════════════
                        تم إنشاء التقرير بواسطة نظام إدارة المخزون
═══════════════════════════════════════════════════════════════════════════════
"""

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)

                show_info_message("تم", f"تم تصدير تقرير المخزون المنخفض بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تصدير التقرير: {str(e)}")

    def view_stock_alerts(self):
        """عرض تنبيهات المخزون"""
        try:
            items = self.session.query(Inventory).all()
            low_stock_items = [item for item in items if (item.quantity or 0) < (item.min_quantity or 5)]

            # إنشاء نافذة التنبيهات
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QListWidget, QListWidgetItem, QPushButton, QHBoxLayout

            dialog = QDialog(self)
            dialog.setWindowTitle("⚠️ تنبيهات المخزون")
            dialog.setModal(True)
            dialog.resize(600, 400)

            layout = QVBoxLayout()

            # معلومات التنبيهات
            info_label = QLabel(f"🔴 عدد العناصر المنخفضة: {len(low_stock_items)}")
            info_label.setStyleSheet("font-weight: bold; font-size: 14px; padding: 10px; background-color: #f8d7da; border-radius: 5px; color: #721c24;")
            layout.addWidget(info_label)

            # قائمة التنبيهات
            alerts_list = QListWidget()
            alerts_list.setStyleSheet("""
                QListWidget {
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    padding: 5px;
                }
                QListWidget::item {
                    padding: 10px;
                    border-bottom: 1px solid #eee;
                    margin: 2px;
                    border-radius: 3px;
                }
                QListWidget::item:selected {
                    background-color: #fff3cd;
                }
            """)

            if low_stock_items:
                for item in low_stock_items:
                    alert_text = f"⚠️ {item.name} - الكمية: {int(item.quantity or 0)} (الحد الأدنى: {int(item.min_quantity or 5)})"
                    list_item = QListWidgetItem(alert_text)
                    alerts_list.addItem(list_item)
            else:
                no_alerts_item = QListWidgetItem("✅ لا توجد تنبيهات - جميع العناصر فوق الحد الأدنى")
                alerts_list.addItem(no_alerts_item)

            layout.addWidget(alerts_list)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            if low_stock_items:
                export_btn = QPushButton("📤 تصدير التقرير")
                export_btn.clicked.connect(lambda: (dialog.close(), self.export_low_stock_report()))
                buttons_layout.addWidget(export_btn)

            close_btn = QPushButton("❌ إغلاق")
            close_btn.clicked.connect(dialog.close)
            buttons_layout.addWidget(close_btn)

            layout.addLayout(buttons_layout)
            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض التنبيهات: {str(e)}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة مطابق للفواتير"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#047857', 'hover_mid': '#059669', 'hover_end': '#10b981', 'hover_bottom': '#34d399',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#052e16', 'pressed_border': '#064e3b',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.5)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#991b1b', 'hover_mid': '#dc2626', 'hover_end': '#ef4444', 'hover_bottom': '#f87171',
                    'hover_border': '#ef4444', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.5)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0284c7', 'bg_bottom': '#0ea5e9',
                    'hover_start': '#075985', 'hover_mid': '#0891b2', 'hover_end': '#0ea5e9', 'hover_bottom': '#38bdf8',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0284c7', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.5)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#14b8a6',
                    'hover_start': '#134e4a', 'hover_mid': '#0d9488', 'hover_end': '#14b8a6', 'hover_bottom': '#2dd4bf',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.5)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#06b6d4',
                    'hover_start': '#164e63', 'hover_mid': '#0891b2', 'hover_end': '#06b6d4', 'hover_bottom': '#22d3ee',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.5)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#ec4899',
                    'hover_start': '#831843', 'hover_mid': '#be185d', 'hover_end': '#ec4899', 'hover_bottom': '#f472b6',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.5)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#6366f1',
                    'hover_start': '#312e81', 'hover_mid': '#4f46e5', 'hover_end': '#6366f1', 'hover_bottom': '#818cf8',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4f46e5', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.5)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#f97316',
                    'hover_start': '#7c2d12', 'hover_mid': '#c2410c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#f97316', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#f97316', 'text': '#ffffff', 'shadow': 'rgba(249, 115, 22, 0.5)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات - مطابق للفواتير
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']},
                               0 0 30px rgba(255, 255, 255, 0.1);
                    letter-spacing: 0.3px;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 4px solid {color_scheme['hover_border']};
                    transform: translateY(-2px);
                    box-shadow: 0 8px 20px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px {color_scheme['shadow']},
                               0 0 40px rgba(255, 255, 255, 0.15);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px);
                    box-shadow: 0 3px 8px {color_scheme['shadow']},
                               inset 0 1px 0 rgba(255, 255, 255, 0.2),
                               inset 0 -1px 0 rgba(0, 0, 0, 0.5),
                               0 0 15px {color_scheme['shadow']};
                }}
                QPushButton::menu-indicator {{
                    {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"}
                }}
            """

            button.setStyleSheet(style)
            print(f"✅ تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق التصميم على الزر: {str(e)}")



    def create_sample_data(self):
        """إنشاء بيانات تجريبية بسيطة للمخزون"""
        try:
            # إضافة صفوف تجريبية للجدول (مع بعض القيم الفارغة لاختبار No Data)
            sample_data = [
                ["1", "دهان أبيض", "دهانات", "100", "50", "25.00", "30.00", "المخزن الرئيسي", "متوفر"],
                ["2", "سيراميك أرضي", "", "200", "150", "15.00", "20.00", "", "متوفر"],  # فئة ومورد فارغ
                ["3", "خشب صنوبر", "أخشاب", "50", "", "100.00", "", "المخزن الرئيسي", "منخفض"],  # حد أدنى وسعر بيع فارغ
                ["4", "", "أدوات كهربائية", "500", "400", "", "8.00", "المخزن الفرعي", "متوفر"],  # اسم وسعر تكلفة فارغ
                ["5", "حنفية مياه", "أدوات صحية", "75", "60", "45.00", "55.00", "", "متوفر"]  # مورد فارغ
            ]

            self.inventory_table.setRowCount(len(sample_data))

            # دالة مساعدة لإنشاء العناصر مطابق للعملاء
            def create_item(icon, text, default="No Data"):
                display_text = text if text and text.strip() else default
                item = QTableWidgetItem(f"{icon} {display_text}")
                item.setTextAlignment(Qt.AlignCenter)
                if display_text == default:
                    item.setForeground(QColor("#ef4444"))
                return item

            for row, data in enumerate(sample_data):
                # الرقم مع أيقونة ثابتة - لون أسود للأرقام مطابق للعملاء
                id_item = QTableWidgetItem(f"🔢 {data[0]}")
                id_item.setTextAlignment(Qt.AlignCenter)
                id_item.setForeground(QColor("#000000"))
                self.inventory_table.setItem(row, 0, id_item)

                # باقي الأعمدة باستخدام create_item
                icons = ["📦", "🏷️", "📊", "📏", "⚠️", "💰", "💵", "🏢"]
                for col in range(1, len(data)):
                    if col < len(icons):
                        icon = icons[col-1]
                        value = data[col] if data[col] else None

                        # تلوين خاص للحالة
                        if col == 8:  # عمود الحالة
                            item = QTableWidgetItem(f"{icon} {value}")
                            item.setTextAlignment(Qt.AlignCenter)
                            if value == "منخفض":
                                item.setForeground(QColor("#dc2626"))
                            elif value == "متوفر":
                                item.setForeground(QColor("#059669"))
                            self.inventory_table.setItem(row, col, item)
                        else:
                            self.inventory_table.setItem(row, col, create_item(icon, value))

            # تحديث الملخص
            self.total_label.setText("إجمالي العناصر: 5 | منخفض المخزون: 1 | القيمة: 12,500.00 ج.م")

            print("✅ تم إنشاء بيانات تجريبية للمخزون")

        except Exception as e:
            print(f"❌ خطأ في إنشاء البيانات التجريبية: {str(e)}")
            # إنشاء جدول فارغ على الأقل
            self.inventory_table.setRowCount(0)
            self.total_label.setText("إجمالي العناصر: 0")

    def create_custom_category_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة للفئات مطابقة للفواتير"""
        # إنشاء إطار للقائمة المخصصة
        self.category_filter_frame = QFrame()
        self.category_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # إنشاء تخطيط أفقي للإطار
        filter_layout = QHBoxLayout(self.category_filter_frame)
        filter_layout.setContentsMargins(5, 0, 5, 0)
        filter_layout.setSpacing(8)

        # سهم يسار (مشابه للسهم الأيمن)
        self.left_arrow = QPushButton("▼")
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)

        # النص الحالي
        self.current_category_label = QLabel("جميع الفئات")
        self.current_category_label.setAlignment(Qt.AlignCenter)  # توسيط النص أفقياً وعمودياً
        self.current_category_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.category_menu_button = QPushButton("▼")
        self.category_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)

        # إضافة العناصر للتخطيط - سهم يسار، النص في المنتصف، سهم يمين
        filter_layout.addWidget(self.left_arrow, 0)
        filter_layout.addWidget(self.current_category_label, 1)
        filter_layout.addWidget(self.category_menu_button, 0)

        # إنشاء القائمة المنسدلة
        self.category_menu = QMenu(self)
        self.category_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                margin: 2px 5px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(96, 165, 250, 0.2);
                margin: 3px 15px;
                border: none;
            }
        """)

        # إضافة خيارات التصفية مع أيقونات مطابقة للعملاء
        filter_options = [
            ("جميع الفئات", None),
            ("🎨 دهانات", "دهانات"),
            ("🏺 سيراميك", "سيراميك"),
            ("🪵 أخشاب", "أخشاب"),
            ("🚿 أدوات صحية", "أدوات صحية"),
            ("⚡ أدوات كهربائية", "أدوات كهربائية"),
            ("🧱 مواد بناء", "مواد بناء"),
            ("📦 أخرى", "أخرى")
        ]

        for text, value in filter_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_category_filter(v, t))
            self.category_menu.addAction(action)

        # ربط الأزرار بالقائمة
        self.category_menu_button.clicked.connect(self.show_category_menu)
        self.left_arrow.clicked.connect(self.show_category_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.category_filter_frame.mousePressEvent = self.category_frame_mouse_press_event
        self.current_category_label.mousePressEvent = self.category_frame_mouse_press_event

        # تعيين القيم الافتراضية
        self.current_category_value = None

    def create_custom_stock_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة للمخزون المنخفض مطابقة للفواتير"""
        # إنشاء إطار للقائمة المخصصة
        self.stock_filter_frame = QFrame()
        self.stock_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # إنشاء تخطيط أفقي للإطار
        stock_layout = QHBoxLayout(self.stock_filter_frame)
        stock_layout.setContentsMargins(5, 0, 5, 0)
        stock_layout.setSpacing(8)

        # سهم يسار
        self.stock_left_arrow = QPushButton("▼")
        self.stock_left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)

        # النص الحالي
        self.current_stock_label = QLabel("الكل")
        self.current_stock_label.setAlignment(Qt.AlignCenter)
        self.current_stock_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.stock_menu_button = QPushButton("▼")
        self.stock_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(139, 92, 246, 0.9);
            }
        """)

        # إضافة العناصر للتخطيط
        stock_layout.addWidget(self.stock_left_arrow, 0)
        stock_layout.addWidget(self.current_stock_label, 1)
        stock_layout.addWidget(self.stock_menu_button, 0)

        # إنشاء القائمة المنسدلة للمخزون
        self.stock_menu = QMenu(self)
        self.stock_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px;
                margin: 2px 5px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                font-family: 'Courier New', 'Consolas', monospace;
                font-smooth: never;
                -webkit-font-smoothing: none;
                -moz-osx-font-smoothing: auto;
                text-rendering: optimizeSpeed;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
        """)

        # إضافة خيارات التصفية للمخزون مع أيقونات مطابقة للعملاء
        stock_options = [
            ("الكل", None),
            ("⚠️ المخزون المنخفض فقط", "low")
        ]

        for text, value in stock_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_stock_filter(v, t))
            self.stock_menu.addAction(action)

        # ربط الأزرار بالقائمة
        self.stock_menu_button.clicked.connect(self.show_stock_menu)
        self.stock_left_arrow.clicked.connect(self.show_stock_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.stock_filter_frame.mousePressEvent = self.stock_frame_mouse_press_event
        self.current_stock_label.mousePressEvent = self.stock_frame_mouse_press_event

        # تعيين القيم الافتراضية
        self.current_stock_value = None

    def show_category_menu(self):
        """عرض قائمة تصفية الفئات"""
        try:
            button = self.sender()
            if button:
                # إذا تم استدعاؤها من زر
                self.category_menu.exec_(button.mapToGlobal(button.rect().bottomLeft()))
            else:
                # إذا تم استدعاؤها من mousePressEvent
                self.category_menu.exec_(self.category_filter_frame.mapToGlobal(self.category_filter_frame.rect().bottomLeft()))
        except Exception as e:
            print(f"خطأ في عرض قائمة الفئات: {str(e)}")
            # عرض القائمة في موقع افتراضي
            self.category_menu.exec_(self.category_filter_frame.mapToGlobal(self.category_filter_frame.rect().bottomLeft()))

    def set_category_filter(self, value, text):
        """تعيين تصفية الفئة"""
        self.current_category_value = value
        self.current_category_label.setText(text)
        self.filter_inventory()

    def category_frame_mouse_press_event(self, event):
        """التعامل مع الضغط على إطار تصفية الفئة"""
        self.show_category_menu()

    def show_stock_menu(self):
        """عرض قائمة تصفية المخزون"""
        try:
            button = self.sender()
            if button:
                # إذا تم استدعاؤها من زر
                self.stock_menu.exec_(button.mapToGlobal(button.rect().bottomLeft()))
            else:
                # إذا تم استدعاؤها من mousePressEvent
                self.stock_menu.exec_(self.stock_filter_frame.mapToGlobal(self.stock_filter_frame.rect().bottomLeft()))
        except Exception as e:
            print(f"خطأ في عرض قائمة المخزون: {str(e)}")
            # عرض القائمة في موقع افتراضي
            self.stock_menu.exec_(self.stock_filter_frame.mapToGlobal(self.stock_filter_frame.rect().bottomLeft()))

    def set_stock_filter(self, value, text):
        """تعيين تصفية المخزون"""
        self.current_stock_value = value
        self.current_stock_label.setText(text)
        self.filter_inventory()

    def stock_frame_mouse_press_event(self, event):
        """التعامل مع الضغط على إطار تصفية المخزون"""
        self.show_stock_menu()
